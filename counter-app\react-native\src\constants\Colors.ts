// 顏色常量定義 - 基於 Fitnest 設計系統

export const Colors = {
  // 主要顏色
  primary: '#92A3FD',
  secondary: '#9DCEFF',
  accent: '#C58BF2',
  success: '#42D742',
  warning: '#FFD600',
  error: '#FF6B6B',
  
  // 中性顏色
  white: '#FFFFFF',
  black: '#000000',
  gray50: '#F7F8F8',
  gray100: '#E6EAEE',
  gray200: '#DDDADA',
  gray300: '#ADA4A5',
  gray400: '#7B6F72',
  gray500: '#1D1617',
  
  // 透明度變化
  primaryLight: '#92A3FD20',
  primaryMedium: '#92A3FD50',
  primaryDark: '#7B8EE8',
  
  secondaryLight: '#9DCEFF20',
  secondaryMedium: '#9DCEFF50',
  secondaryDark: '#85B8E8',
  
  accentLight: '#C58BF220',
  accentMedium: '#C58BF250',
  accentDark: '#B076E0',
  
  // 狀態顏色
  successLight: '#42D74220',
  successMedium: '#42D74250',
  successDark: '#38C238',
  
  warningLight: '#FFD60020',
  warningMedium: '#FFD60050',
  warningDark: '#E8C100',
  
  errorLight: '#FF6B6B20',
  errorMedium: '#FF6B6B50',
  errorDark: '#E85555',
  
  // 背景顏色
  background: '#FFFFFF',
  backgroundSecondary: '#F7F8F8',
  backgroundTertiary: '#E6EAEE',
  
  // 文字顏色
  textPrimary: '#1D1617',
  textSecondary: '#7B6F72',
  textTertiary: '#ADA4A5',
  textInverse: '#FFFFFF',
  
  // 邊框顏色
  border: '#E6EAEE',
  borderLight: '#F7F8F8',
  borderDark: '#DDDADA',
  
  // 陰影顏色
  shadow: '#00000010',
  shadowMedium: '#00000020',
  shadowDark: '#00000030',
  
  // 覆蓋層顏色
  overlay: '#00000050',
  overlayDark: '#00000080',
  
  // 漸變顏色組合
  gradientPrimary: ['#92A3FD', '#9DCEFF'],
  gradientSecondary: ['#C58BF2', '#EEA4CE'],
  gradientSuccess: ['#42D742', '#7ED321'],
  gradientWarning: ['#FFD600', '#FFB800'],
  gradientError: ['#FF6B6B', '#FF8A80'],
  
  // 特殊用途顏色
  cardBackground: '#FFFFFF',
  modalBackground: '#FFFFFF',
  tabBarBackground: '#FFFFFF',
  headerBackground: '#FFFFFF',
  
  // 計數器專用顏色
  counterButton: '#92A3FD',
  counterButtonPressed: '#7B8EE8',
  progressRing: '#92A3FD',
  progressBackground: '#E6EAEE',
  
  // 願望清單專用顏色
  wishCard: '#FFFFFF',
  wishBlessedBorder: '#42D742',
  wishCategoryHealth: '#42D742',
  wishCategoryFamily: '#FF6B6B',
  wishCategoryCareer: '#FFD600',
  wishCategoryStudy: '#92A3FD',
  wishCategoryPeace: '#C58BF2',
  wishCategoryOther: '#ADA4A5',
  
  // 統計圖表顏色
  chartPrimary: '#92A3FD',
  chartSecondary: '#9DCEFF',
  chartTertiary: '#C58BF2',
  chartSuccess: '#42D742',
  chartWarning: '#FFD600',
  chartError: '#FF6B6B',
  
  // 深色主題顏色（預留）
  dark: {
    primary: '#92A3FD',
    secondary: '#9DCEFF',
    background: '#1A1A1A',
    surface: '#2D2D2D',
    text: '#FFFFFF',
    textSecondary: '#CCCCCC',
    border: '#404040',
  },
} as const;

// 顏色工具函數
export const ColorUtils = {
  // 添加透明度
  withOpacity: (color: string, opacity: number): string => {
    const hex = color.replace('#', '');
    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
    return `#${hex}${alpha}`;
  },
  
  // 獲取願望分類顏色
  getWishCategoryColor: (category: string): string => {
    switch (category) {
      case 'health': return Colors.wishCategoryHealth;
      case 'family': return Colors.wishCategoryFamily;
      case 'career': return Colors.wishCategoryCareer;
      case 'study': return Colors.wishCategoryStudy;
      case 'peace': return Colors.wishCategoryPeace;
      default: return Colors.wishCategoryOther;
    }
  },
  
  // 獲取漸變顏色
  getGradientColors: (type: 'primary' | 'secondary' | 'success' | 'warning' | 'error'): string[] => {
    switch (type) {
      case 'primary': return Colors.gradientPrimary;
      case 'secondary': return Colors.gradientSecondary;
      case 'success': return Colors.gradientSuccess;
      case 'warning': return Colors.gradientWarning;
      case 'error': return Colors.gradientError;
      default: return Colors.gradientPrimary;
    }
  },
  
  // 判斷顏色是否為深色
  isDark: (color: string): boolean => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness < 128;
  },
  
  // 獲取對比文字顏色
  getContrastText: (backgroundColor: string): string => {
    return ColorUtils.isDark(backgroundColor) ? Colors.white : Colors.black;
  },
};

// 主題配置
export const ThemeConfig = {
  light: {
    colors: Colors,
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
    },
    borderRadius: {
      sm: 8,
      md: 12,
      lg: 16,
      xl: 20,
      full: 9999,
    },
    fontSize: {
      xs: 10,
      sm: 12,
      md: 14,
      lg: 16,
      xl: 18,
      xxl: 20,
      xxxl: 24,
    },
    fontWeight: {
      light: '300' as const,
      normal: '400' as const,
      medium: '500' as const,
      semibold: '600' as const,
      bold: '700' as const,
    },
    shadows: {
      sm: {
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
      },
      md: {
        shadowColor: Colors.shadowMedium,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 4,
      },
      lg: {
        shadowColor: Colors.shadowDark,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
      },
    },
  },
};

export type Theme = typeof ThemeConfig.light;
