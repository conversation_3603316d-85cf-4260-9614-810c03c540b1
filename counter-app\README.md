# 咒語計數器 - 修行助手

基於 Fitnest UI 設計風格的現代化修行計數應用，幫助修行者記錄持誦功課、管理願望清單、追蹤修行進度。

## 📱 應用概述

咒語計數器是一個專為佛教修行者設計的數位工具，提供完整的修行管理功能。應用採用現代化的 UI 設計，結合傳統修行理念，讓修行變得更加便利和有意義。

## 🎨 設計特色

- **現代化 UI**: 採用 Fitnest 健身應用的設計語言，漸變色彩和圓角設計
- **直觀操作**: 極簡的計數界面，專注於修行體驗
- **功德迴向**: 完成修行後可進行功德迴向和願望灌注
- **數據追蹤**: 詳細的修行統計和進度追蹤
- **響應式設計**: 適配手機、平板等各種設備

## 🚀 核心功能

### 1. 修行項目管理
- **創建項目**: 設定修行項目名稱、目標數量和描述
- **進度追蹤**: 實時顯示修行進度和完成百分比
- **項目統計**: 查看總持誦次數、會期數量等統計信息

### 2. 計數功能
- **極簡計數**: 大按鈕設計，支持觸摸和鍵盤操作
- **進度環**: 視覺化顯示當前會期進度
- **快速操作**: +10、-1、重置等快速操作
- **時間統計**: 記錄修行時間和平均速度

### 3. 願望清單
- **願望管理**: 添加、編輯、分類個人願望
- **功德灌注**: 將修行功德灌注到特定願望
- **祝福記錄**: 查看願望的祝福歷史和次數

### 4. 功德迴向
- **迴向文編輯**: 自定義或使用預設迴向文模板
- **願望選擇**: 選擇要灌注功德的願望
- **社群分享**: 分享修行成果到社交平台

### 5. 數據統計
- **今日統計**: 當日修行次數和持誦總數
- **歷史記錄**: 查看過往修行會期記錄
- **進度分析**: 修行進度和趨勢分析

## 📁 項目結構

```
counter-app/
├── index.html              # 主頁面 - 項目管理和統計
├── counter.html            # 計數頁面 - 極簡計數界面
├── wishlist.html           # 願望清單 - 願望管理
├── merit.html              # 功德迴向 - 迴向和分享
├── css/
│   ├── styles.css          # 主樣式文件
│   ├── counter.css         # 計數頁面樣式
│   ├── wishlist.css        # 願望清單樣式
│   └── merit.css           # 迴向頁面樣式
├── js/
│   ├── storage.js          # 本地存儲管理
│   ├── app.js              # 主應用邏輯
│   ├── counter.js          # 計數頁面邏輯
│   ├── wishlist.js         # 願望清單邏輯
│   └── merit.js            # 迴向頁面邏輯
├── 功能說明.md             # 原始需求文檔
└── README.md               # 項目說明文檔
```

## 🎯 頁面功能詳解

### 主頁面 (index.html)
- **歡迎橫幅**: 根據時間顯示不同問候語
- **今日統計**: 顯示當日修行次數和持誦總數
- **項目列表**: 展示所有修行項目和進度
- **最近完成**: 顯示最近的修行記錄
- **底部導航**: 快速切換到其他功能頁面

### 計數頁面 (counter.html)
- **項目信息**: 顯示當前修行項目和總進度
- **進度環**: 視覺化顯示當前會期進度
- **計數按鈕**: 大型觸摸友好的計數按鈕
- **快速操作**: +10、-1、重置等輔助功能
- **會期統計**: 開始時間、持續時間、平均速度

### 願望清單 (wishlist.html)
- **願望統計**: 總願望數和已祝福數量
- **願望列表**: 支持分類篩選的願望列表
- **願望管理**: 添加、編輯、刪除願望功能
- **祝福記錄**: 查看功德灌注歷史記錄

### 功德迴向 (merit.html)
- **修行摘要**: 顯示剛完成的修行會期信息
- **迴向文編輯**: 自定義迴向文或使用模板
- **願望選擇**: 選擇要灌注功德的願望
- **分享功能**: 分享修行成果到社交平台

## 🛠️ 技術實現

### 前端技術
- **HTML5**: 語義化標籤、無障礙支持
- **CSS3**: CSS 變量、Grid/Flexbox、動畫效果
- **JavaScript ES6+**: 類語法、模塊化設計、本地存儲

### 設計系統
- **色彩方案**: 基於 Fitnest 的漸變色彩系統
- **字體**: Poppins 字體家族
- **間距**: 統一的間距系統 (4px 基準)
- **圓角**: 一致的圓角設計語言

### 數據存儲
- **LocalStorage**: 本地數據持久化
- **JSON 格式**: 結構化數據存儲
- **數據模型**: 項目、會期、願望、祝福記錄

## 📱 使用方法

### 開始修行
1. 在主頁點擊「+」創建修行項目
2. 設定項目名稱和目標數量
3. 點擊項目卡片開始修行會期
4. 設定本次目標次數
5. 進入計數頁面開始持誦

### 計數操作
- **點擊大按鈕**: 增加計數
- **+10 按鈕**: 快速增加 10 次
- **-1 按鈕**: 減少 1 次計數
- **重置按鈕**: 重置當前會期
- **空格鍵**: 鍵盤快捷鍵計數

### 完成修行
1. 達到目標次數自動彈出完成提示
2. 選擇「迴向功德」進入迴向頁面
3. 編輯迴向文或使用預設模板
4. 選擇要灌注的願望
5. 完成迴向或分享到社群

### 願望管理
1. 在願望清單頁面添加願望
2. 設定願望類別（健康、家庭等）
3. 修行完成後可將功德灌注到願望
4. 查看願望的祝福次數和記錄

## 🎮 交互特性

### 觸摸體驗
- **大按鈕設計**: 適合觸摸操作的大型按鈕
- **觸覺反饋**: 支持震動反饋（可設定）
- **音效提示**: 計數和完成音效（可設定）

### 視覺反饋
- **進度動畫**: 流暢的進度條和進度環動畫
- **狀態變化**: 清晰的視覺狀態反饋
- **載入動畫**: 頁面切換和數據載入動畫

### 數據同步
- **實時更新**: 數據變更即時反映到界面
- **狀態保持**: 頁面刷新後保持應用狀態
- **會期恢復**: 意外中斷後可恢復計數會期

## 🔮 未來擴展

### 計劃功能
- [ ] 雲端同步功能
- [ ] 多設備數據同步
- [ ] 修行提醒和鬧鐘
- [ ] 修行日曆和計劃
- [ ] 社群功能和排行榜
- [ ] 更多迴向文模板
- [ ] 修行音樂播放器
- [ ] 數據導出和備份

### 技術改進
- [ ] PWA 支持（離線使用）
- [ ] 暗色主題模式
- [ ] 多語言支持
- [ ] 性能優化
- [ ] 無障礙功能增強

## 📄 許可證

本項目基於 MIT 許可證開源。

## 🙏 致謝

- **設計靈感**: Fitnest 健身應用 UI 套件
- **圖標**: Material Design Icons
- **字體**: Google Fonts (Poppins)
- **開發框架**: 原生 Web 技術

---

**願此應用能幫助更多修行者精進修行，功德無量！** 🙏
