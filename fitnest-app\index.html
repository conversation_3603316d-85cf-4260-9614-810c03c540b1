<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fitnest - 健身應用</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 主容器 -->
    <div class="app-container">
        <!-- 頂部導航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">Fitnest</h1>
                <div class="header-actions">
                    <button class="icon-btn notification-btn">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 5.5V4.5C19 3.1 17.9 2 16.5 2S14 3.1 14 4.5V5.5L12 7V9C12 10.1 12.9 11 14 11V12L12 14H10V12C8.9 12 8 11.1 8 10V9L6 7V5.5C6 4.1 4.9 3 3.5 3S1 4.1 1 5.5V6.5L3 8V10C3 11.1 3.9 12 5 12V14L7 16H9V14C10.1 14 11 13.1 11 12V11C12.1 11 13 10.1 13 9Z"/>
                        </svg>
                    </button>
                    <button class="icon-btn profile-btn">
                        <div class="profile-avatar"></div>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要內容區域 -->
        <main class="main-content">
            <!-- 歡迎橫幅 -->
            <section class="welcome-banner">
                <div class="banner-content">
                    <h2>歡迎回來！</h2>
                    <p>讓我們一起達成今天的健身目標</p>
                </div>
                <div class="banner-graphic">
                    <div class="activity-chart"></div>
                </div>
            </section>

            <!-- 今日目標卡片 -->
            <section class="today-target">
                <h3>今日目標</h3>
                <div class="target-card">
                    <div class="target-info">
                        <span class="target-label">活動卡路里</span>
                        <span class="target-value">760 / 900</span>
                    </div>
                    <div class="progress-ring">
                        <svg class="progress-svg" viewBox="0 0 100 100">
                            <defs>
                                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#92A3FD;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#9DCEFF;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <circle cx="50" cy="50" r="45" class="progress-bg"/>
                            <circle cx="50" cy="50" r="45" class="progress-fill" style="--progress: 84.4%"/>
                        </svg>
                        <span class="progress-text">84%</span>
                    </div>
                </div>
            </section>

            <!-- 狀態卡片網格 -->
            <section class="status-grid">
                <div class="status-card heart-rate">
                    <div class="card-header">
                        <span class="card-title">心率</span>
                        <svg class="card-icon" viewBox="0 0 24 24">
                            <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.04L12,21.35Z"/>
                        </svg>
                    </div>
                    <div class="card-value">78 <span class="unit">BPM</span></div>
                    <div class="card-chart heart-chart"></div>
                </div>

                <div class="status-card water-intake">
                    <div class="card-header">
                        <span class="card-title">水分攝取</span>
                        <svg class="card-icon" viewBox="0 0 24 24">
                            <path d="M12,2A1,1 0 0,1 13,3A1,1 0 0,1 12,4A1,1 0 0,1 11,3A1,1 0 0,1 12,2M21,9V7L19,5.5V4.5A1.5,1.5 0 0,0 17.5,3A1.5,1.5 0 0,0 16,4.5V5.5L14,7V9A1,1 0 0,0 15,10V11L17,13H19V11A1,1 0 0,0 20,10V9M3,9V7L1,5.5V4.5A1.5,1.5 0 0,1 2.5,3A1.5,1.5 0 0,1 4,4.5V5.5L6,7V9A1,1 0 0,1 5,10V11L3,13H1V11A1,1 0 0,1 2,10V9"/>
                        </svg>
                    </div>
                    <div class="card-value">2.5 <span class="unit">L</span></div>
                    <div class="water-progress">
                        <div class="water-fill" style="--fill: 62.5%"></div>
                    </div>
                </div>

                <div class="status-card sleep">
                    <div class="card-header">
                        <span class="card-title">睡眠</span>
                        <svg class="card-icon" viewBox="0 0 24 24">
                            <path d="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z"/>
                        </svg>
                    </div>
                    <div class="card-value">7h 30m</div>
                    <div class="sleep-chart"></div>
                </div>

                <div class="status-card calories">
                    <div class="card-header">
                        <span class="card-title">卡路里</span>
                        <svg class="card-icon" viewBox="0 0 24 24">
                            <path d="M15.5,5A6.5,6.5 0 0,1 22,11.5A6.5,6.5 0 0,1 15.5,18A6.5,6.5 0 0,1 9,11.5A6.5,6.5 0 0,1 15.5,5M15.5,7A4.5,4.5 0 0,0 11,11.5A4.5,4.5 0 0,0 15.5,16A4.5,4.5 0 0,0 20,11.5A4.5,4.5 0 0,0 15.5,7M2,11.5A6.5,6.5 0 0,1 8.5,5A6.5,6.5 0 0,1 15,11.5A6.5,6.5 0 0,1 8.5,18A6.5,6.5 0 0,1 2,11.5M8.5,7A4.5,4.5 0 0,0 4,11.5A4.5,4.5 0 0,0 8.5,16A4.5,4.5 0 0,0 13,11.5A4.5,4.5 0 0,0 8.5,7Z"/>
                        </svg>
                    </div>
                    <div class="card-value">1,847 <span class="unit">Cal</span></div>
                    <div class="calories-chart"></div>
                </div>
            </section>

            <!-- 最新運動 -->
            <section class="latest-workout">
                <div class="section-header">
                    <h3>最新運動</h3>
                    <button class="see-more-btn">查看更多</button>
                </div>
                <div class="workout-cards">
                    <div class="workout-card active">
                        <div class="workout-image fullbody-bg"></div>
                        <div class="workout-info">
                            <h4>全身運動</h4>
                            <p>11 個練習 | 32 分鐘</p>
                        </div>
                        <button class="workout-action-btn">
                            <svg viewBox="0 0 24 24">
                                <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                            </svg>
                        </button>
                    </div>

                    <div class="workout-card">
                        <div class="workout-image lowerbody-bg"></div>
                        <div class="workout-info">
                            <h4>下半身運動</h4>
                            <p>12 個練習 | 40 分鐘</p>
                        </div>
                        <button class="workout-action-btn">
                            <svg viewBox="0 0 24 24">
                                <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                            </svg>
                        </button>
                    </div>

                    <div class="workout-card">
                        <div class="workout-image ab-bg"></div>
                        <div class="workout-info">
                            <h4>腹部運動</h4>
                            <p>14 個練習 | 20 分鐘</p>
                        </div>
                        <button class="workout-action-btn">
                            <svg viewBox="0 0 24 24">
                                <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部導航 -->
        <nav class="bottom-nav">
            <button class="nav-item active">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"/>
                </svg>
                <span class="nav-label">首頁</span>
            </button>
            <button class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M9,10V12H7V10H9M13,10V12H11V10H13M17,10V12H15V10H17M19,3A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5A2,2 0 0,1 5,3H6V1H8V3H16V1H18V3H19M19,19V8H5V19H19M9,14V16H7V14H9M13,14V16H11V14H13M17,14V16H15V14H17Z"/>
                </svg>
                <span class="nav-label">活動</span>
            </button>
            <button class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                </svg>
                <span class="nav-label">搜索</span>
            </button>
            <button class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9A3,3 0 0,0 9,12M12,4.5C17,4.5 21.27,7.61 23,12C21.27,16.39 17,19.5 12,19.5C7,19.5 2.73,16.39 1,12C2.73,7.61 7,4.5 12,4.5M3.18,12C4.83,15.36 8.24,17.5 12,17.5C15.76,17.5 19.17,15.36 20.82,12C19.17,8.64 15.76,6.5 12,6.5C8.24,6.5 4.83,8.64 3.18,12Z"/>
                </svg>
                <span class="nav-label">相機</span>
            </button>
            <button class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                </svg>
                <span class="nav-label">個人資料</span>
            </button>
        </nav>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
