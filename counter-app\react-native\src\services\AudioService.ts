import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';
import { SoundType, HapticFeedbackType } from '../types';
import { StorageService } from './StorageService';

export class AudioService {
  private static sounds: { [key in SoundType]?: Audio.Sound } = {};
  private static isInitialized = false;

  // 初始化音效服務
  static async initialize(): Promise<void> {
    try {
      // 設置音頻模式
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
        playThroughEarpieceAndroid: false,
      });

      // 預加載音效文件
      await this.preloadSounds();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Audio service initialization failed:', error);
    }
  }

  // 預加載音效文件
  private static async preloadSounds(): Promise<void> {
    const soundFiles: { [key in SoundType]: any } = {
      click: require('../../assets/sounds/click.mp3'),
      success: require('../../assets/sounds/success.mp3'),
      complete: require('../../assets/sounds/complete.mp3'),
      error: require('../../assets/sounds/error.mp3'),
      notification: require('../../assets/sounds/notification.mp3'),
    };

    try {
      for (const [soundType, soundFile] of Object.entries(soundFiles)) {
        const { sound } = await Audio.Sound.createAsync(soundFile, {
          shouldPlay: false,
          volume: 0.5,
        });
        this.sounds[soundType as SoundType] = sound;
      }
    } catch (error) {
      console.error('Failed to preload sounds:', error);
    }
  }

  // 播放音效
  static async playSound(type: SoundType, volume: number = 0.5): Promise<void> {
    try {
      // 檢查設置是否啟用音效
      const settings = await StorageService.getSettings();
      if (!settings?.soundEnabled) return;

      if (!this.isInitialized) {
        await this.initialize();
      }

      const sound = this.sounds[type];
      if (sound) {
        await sound.setVolumeAsync(volume);
        await sound.replayAsync();
      } else {
        // 如果預加載失敗，嘗試直接播放
        await this.playDirectSound(type, volume);
      }
    } catch (error) {
      console.error(`Failed to play sound ${type}:`, error);
    }
  }

  // 直接播放音效（備用方法）
  private static async playDirectSound(type: SoundType, volume: number): Promise<void> {
    try {
      const soundFiles: { [key in SoundType]: any } = {
        click: require('../../assets/sounds/click.mp3'),
        success: require('../../assets/sounds/success.mp3'),
        complete: require('../../assets/sounds/complete.mp3'),
        error: require('../../assets/sounds/error.mp3'),
        notification: require('../../assets/sounds/notification.mp3'),
      };

      const soundFile = soundFiles[type];
      if (soundFile) {
        const { sound } = await Audio.Sound.createAsync(soundFile, {
          shouldPlay: true,
          volume,
        });
        
        // 播放完成後釋放資源
        sound.setOnPlaybackStatusUpdate((status) => {
          if (status.isLoaded && status.didJustFinish) {
            sound.unloadAsync();
          }
        });
      }
    } catch (error) {
      console.error(`Failed to play direct sound ${type}:`, error);
    }
  }

  // 觸覺反饋
  static async hapticFeedback(type: HapticFeedbackType): Promise<void> {
    try {
      // 檢查設置是否啟用震動
      const settings = await StorageService.getSettings();
      if (!settings?.vibrationEnabled) return;

      switch (type) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'warning':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'error':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
        default:
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      console.error(`Failed to provide haptic feedback ${type}:`, error);
    }
  }

  // 組合音效和觸覺反饋
  static async playFeedback(
    soundType: SoundType, 
    hapticType: HapticFeedbackType,
    soundVolume: number = 0.5
  ): Promise<void> {
    await Promise.all([
      this.playSound(soundType, soundVolume),
      this.hapticFeedback(hapticType),
    ]);
  }

  // 計數器專用反饋
  static async counterClick(): Promise<void> {
    await this.playFeedback('click', 'light', 0.3);
  }

  static async counterComplete(): Promise<void> {
    await this.playFeedback('complete', 'success', 0.7);
  }

  static async counterError(): Promise<void> {
    await this.playFeedback('error', 'error', 0.5);
  }

  // 界面交互反饋
  static async buttonPress(): Promise<void> {
    await this.hapticFeedback('light');
  }

  static async buttonSuccess(): Promise<void> {
    await this.playFeedback('success', 'success', 0.5);
  }

  static async buttonError(): Promise<void> {
    await this.playFeedback('error', 'error', 0.5);
  }

  // 通知音效
  static async notification(): Promise<void> {
    await this.playFeedback('notification', 'medium', 0.6);
  }

  // 設置音量
  static async setVolume(type: SoundType, volume: number): Promise<void> {
    try {
      const sound = this.sounds[type];
      if (sound) {
        await sound.setVolumeAsync(Math.max(0, Math.min(1, volume)));
      }
    } catch (error) {
      console.error(`Failed to set volume for ${type}:`, error);
    }
  }

  // 設置所有音效音量
  static async setGlobalVolume(volume: number): Promise<void> {
    const normalizedVolume = Math.max(0, Math.min(1, volume));
    
    for (const soundType of Object.keys(this.sounds) as SoundType[]) {
      await this.setVolume(soundType, normalizedVolume);
    }
  }

  // 暫停所有音效
  static async pauseAll(): Promise<void> {
    try {
      for (const sound of Object.values(this.sounds)) {
        if (sound) {
          await sound.pauseAsync();
        }
      }
    } catch (error) {
      console.error('Failed to pause all sounds:', error);
    }
  }

  // 停止所有音效
  static async stopAll(): Promise<void> {
    try {
      for (const sound of Object.values(this.sounds)) {
        if (sound) {
          await sound.stopAsync();
        }
      }
    } catch (error) {
      console.error('Failed to stop all sounds:', error);
    }
  }

  // 釋放資源
  static async cleanup(): Promise<void> {
    try {
      for (const sound of Object.values(this.sounds)) {
        if (sound) {
          await sound.unloadAsync();
        }
      }
      this.sounds = {};
      this.isInitialized = false;
    } catch (error) {
      console.error('Failed to cleanup audio service:', error);
    }
  }

  // 檢查音效是否可用
  static async isAudioAvailable(): Promise<boolean> {
    try {
      const audioMode = await Audio.getAudioModeAsync();
      return audioMode.allowsRecordingIOS !== undefined;
    } catch (error) {
      console.error('Failed to check audio availability:', error);
      return false;
    }
  }

  // 檢查觸覺反饋是否可用
  static async isHapticsAvailable(): Promise<boolean> {
    try {
      return await Haptics.isAvailableAsync();
    } catch (error) {
      console.error('Failed to check haptics availability:', error);
      return false;
    }
  }

  // 測試音效
  static async testSound(type: SoundType): Promise<boolean> {
    try {
      await this.playSound(type, 0.5);
      return true;
    } catch (error) {
      console.error(`Failed to test sound ${type}:`, error);
      return false;
    }
  }

  // 測試觸覺反饋
  static async testHaptic(type: HapticFeedbackType): Promise<boolean> {
    try {
      await this.hapticFeedback(type);
      return true;
    } catch (error) {
      console.error(`Failed to test haptic ${type}:`, error);
      return false;
    }
  }

  // 獲取音效狀態
  static async getSoundStatus(type: SoundType): Promise<any> {
    try {
      const sound = this.sounds[type];
      if (sound) {
        return await sound.getStatusAsync();
      }
      return null;
    } catch (error) {
      console.error(`Failed to get sound status for ${type}:`, error);
      return null;
    }
  }

  // 創建自定義音效
  static async createCustomSound(uri: string, type: string): Promise<boolean> {
    try {
      const { sound } = await Audio.Sound.createAsync({ uri }, {
        shouldPlay: false,
        volume: 0.5,
      });
      
      this.sounds[type as SoundType] = sound;
      return true;
    } catch (error) {
      console.error(`Failed to create custom sound ${type}:`, error);
      return false;
    }
  }
}
