# 🚀 咒語計數器測試執行指南

本指南將引導您完成咒語計數器應用的完整測試流程，確保應用在上架前達到商業級質量標準。

## 📋 測試前準備

### 1. 環境檢查
```bash
# 檢查 Node.js 版本 (需要 18+)
node --version

# 檢查 npm 版本
npm --version

# 檢查 Expo CLI
npx expo --version

# 檢查 Git 狀態
git status
```

### 2. 依賴安裝
```bash
# 安裝項目依賴
npm install

# 安裝全局工具
npm install -g detox-cli
npm install -g eas-cli
```

### 3. 環境配置
```bash
# 複製環境配置文件
cp .env.example .env

# 設置 EAS 項目
eas login
eas init
```

## 🧪 測試執行步驟

### 第一階段：快速檢查 (5 分鐘)

```bash
# 1. TypeScript 類型檢查
npm run type-check

# 2. 代碼格式檢查
npm run format:check

# 3. 代碼規範檢查
npm run lint
```

**預期結果**: 所有檢查都應該通過，無錯誤或警告。

### 第二階段：單元測試 (10 分鐘)

```bash
# 運行所有單元測試
npm run test:ci

# 查看覆蓋率報告
open coverage/lcov-report/index.html
```

**預期結果**: 
- 所有測試通過
- 代碼覆蓋率 ≥ 80%
- 無內存洩漏

### 第三階段：構建測試 (15 分鐘)

```bash
# 1. 本地構建測試
npm run build:preview

# 2. Bundle 大小檢查
npm run analyze

# 3. EAS 構建驗證
eas build --platform all --dry-run
```

**預期結果**:
- 構建成功無錯誤
- Bundle 大小合理 (< 50MB)
- EAS 配置正確

### 第四階段：設備測試 (30 分鐘)

#### iOS 測試
```bash
# 1. 啟動 iOS 模擬器
open -a Simulator

# 2. 運行開發版本
npm run ios

# 3. 手動測試核心功能
# - 創建項目
# - 計數功能
# - 願望清單
# - 功德迴向
```

#### Android 測試
```bash
# 1. 啟動 Android 模擬器
emulator -avd Pixel_4_API_30

# 2. 運行開發版本
npm run android

# 3. 重複 iOS 的手動測試
```

### 第五階段：E2E 測試 (20 分鐘)

```bash
# 1. 構建測試應用
npm run test:e2e:build

# 2. 運行 E2E 測試
npm run test:e2e

# 3. 查看測試報告
open e2e/artifacts/
```

**預期結果**:
- 所有 E2E 測試通過
- 用戶流程完整無阻斷
- 性能表現良好

### 第六階段：完整測試套件 (45 分鐘)

```bash
# 運行完整測試套件
npm run test:all
```

這將自動執行：
- 代碼質量檢查
- 單元測試
- 構建測試
- 性能測試
- 安全檢查
- 商店準備檢查

## 📊 測試結果評估

### 成功標準

#### 功能測試
- [ ] 所有核心功能正常工作
- [ ] 用戶流程完整無阻斷
- [ ] 錯誤處理完善
- [ ] 數據持久化正常

#### 性能測試
- [ ] 應用啟動時間 < 3 秒
- [ ] 頁面切換流暢 (< 300ms)
- [ ] 內存使用合理 (< 200MB)
- [ ] CPU 使用率正常 (< 50%)

#### 質量測試
- [ ] 代碼覆蓋率 ≥ 80%
- [ ] 無嚴重 ESLint 錯誤
- [ ] 無 TypeScript 類型錯誤
- [ ] 無安全漏洞

#### 兼容性測試
- [ ] iOS 13.0+ 支持
- [ ] Android API 21+ 支持
- [ ] 不同屏幕尺寸適配
- [ ] 橫豎屏切換正常

## 🚨 常見問題處理

### 測試失敗處理

#### 單元測試失敗
```bash
# 查看詳細錯誤信息
npm test -- --verbose

# 運行特定測試文件
npm test StorageService.test.ts

# 更新快照
npm test -- --updateSnapshot
```

#### E2E 測試失敗
```bash
# 查看測試截圖
open e2e/artifacts/

# 重新構建測試應用
detox clean-framework-cache
npm run test:e2e:build

# 單獨運行失敗的測試
detox test --testNamePattern="項目管理"
```

#### 構建失敗
```bash
# 清除緩存
npm run clean:cache

# 檢查依賴
npm audit fix

# 重新安裝依賴
rm -rf node_modules package-lock.json
npm install
```

### 性能問題處理

#### 內存洩漏
```bash
# 運行內存洩漏檢測
npm test -- --detectLeaks

# 使用 Flipper 分析
npx flipper
```

#### 啟動緩慢
```bash
# 分析 Bundle 大小
npm run analyze

# 檢查不必要的依賴
npx depcheck

# 優化圖片資源
npx imagemin-cli assets/**/*.png --out-dir=assets/optimized/
```

## 📱 設備測試清單

### 必測設備
- [ ] **iPhone 12 Mini** (iOS 15+)
- [ ] **iPhone 14** (iOS 16+)
- [ ] **iPhone 14 Pro Max** (iOS 16+)
- [ ] **iPad** (iPadOS 15+)
- [ ] **Pixel 4** (Android 10)
- [ ] **Samsung Galaxy S21** (Android 11+)
- [ ] **小米手機** (MIUI 12+)

### 測試場景
- [ ] **首次安裝** - 歡迎流程
- [ ] **正常使用** - 日常功能
- [ ] **數據恢復** - 重新安裝後數據恢復
- [ ] **網絡異常** - 離線使用
- [ ] **低電量** - 省電模式下使用
- [ ] **後台切換** - 應用切換和恢復

## 📋 上架前最終檢查

### 技術檢查
- [ ] 所有測試通過
- [ ] 性能指標達標
- [ ] 無嚴重 Bug
- [ ] 代碼質量良好

### 內容檢查
- [ ] 應用圖標完整
- [ ] 啟動畫面正確
- [ ] 應用截圖準備
- [ ] 商店描述完整

### 法律檢查
- [ ] 隱私政策完整
- [ ] 使用條款完整
- [ ] 版權信息正確
- [ ] 內容分級合適

### 商業檢查
- [ ] 應用名稱確定
- [ ] 關鍵詞優化
- [ ] 定價策略確定
- [ ] 發布計劃制定

## 🎯 測試完成標準

當以下所有條件都滿足時，應用即可準備上架：

1. **功能完整性**: 100% 核心功能正常
2. **測試覆蓋率**: ≥ 80% 代碼覆蓋率
3. **性能表現**: 所有性能指標達標
4. **質量標準**: 無嚴重質量問題
5. **兼容性**: 支持目標平台和設備
6. **用戶體驗**: 用戶流程順暢完整
7. **商店合規**: 符合應用商店要求

## 📞 支援聯絡

如果在測試過程中遇到問題：

1. **查看日誌**: 檢查詳細的錯誤日誌
2. **搜索文檔**: 查看相關測試文檔
3. **社群求助**: 在開發者社群提問
4. **專業支援**: 聯絡技術支援團隊

---

**記住**: 充分的測試是成功上架的關鍵。每一分鐘的測試投入都會在上架後得到回報！ 🚀

