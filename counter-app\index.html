<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咒語計數器 - 修行助手</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 頂部導航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">咒語計數器</h1>
                <div class="header-actions">
                    <button class="icon-btn notification-btn" onclick="showStats()">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
                        </svg>
                    </button>
                    <button class="icon-btn add-btn" onclick="showCreateProject()">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要內容區域 -->
        <main class="main-content">
            <!-- 歡迎橫幅 -->
            <section class="welcome-banner">
                <div class="banner-content">
                    <h2 id="greeting">開始今日修行</h2>
                    <p>願所有功德迴向眾生</p>
                </div>
                <div class="banner-graphic">
                    <div class="meditation-icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z"/>
                        </svg>
                    </div>
                </div>
            </section>

            <!-- 今日統計 -->
            <section class="today-stats">
                <h3>今日修行</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z"/>
                            </svg>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="todaySessions">0</span>
                            <span class="stat-label">修行次數</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12,2L13.09,8.26L22,9L14.74,13.74L17.18,22L12,17.27L6.82,22L9.26,13.74L2,9L10.91,8.26L12,2Z"/>
                            </svg>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="todayCount">0</span>
                            <span class="stat-label">持誦總數</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 修行項目列表 -->
            <section class="projects-section">
                <div class="section-header">
                    <h3>修行項目</h3>
                    <button class="see-more-btn" onclick="showAllProjects()">查看全部</button>
                </div>
                <div class="projects-list" id="projectsList">
                    <!-- 項目將通過 JavaScript 動態生成 -->
                </div>
                
                <!-- 空狀態 -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
                        </svg>
                    </div>
                    <h4>開始您的修行之旅</h4>
                    <p>點擊右上角的 + 號創建第一個修行項目</p>
                    <button class="primary-btn" onclick="showCreateProject()">創建項目</button>
                </div>
            </section>

            <!-- 最近完成 -->
            <section class="recent-section">
                <div class="section-header">
                    <h3>最近完成</h3>
                    <button class="see-more-btn" onclick="showHistory()">查看歷史</button>
                </div>
                <div class="recent-list" id="recentList">
                    <!-- 最近完成的會期將通過 JavaScript 動態生成 -->
                </div>
            </section>
        </main>

        <!-- 底部導航 -->
        <nav class="bottom-nav">
            <button class="nav-item active">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"/>
                </svg>
                <span class="nav-label">首頁</span>
            </button>
            <button class="nav-item" onclick="showWishList()">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.04L12,21.35Z"/>
                </svg>
                <span class="nav-label">願望清單</span>
            </button>
            <button class="nav-item" onclick="showHistory()">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3"/>
                </svg>
                <span class="nav-label">歷史</span>
            </button>
            <button class="nav-item" onclick="showStats()">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
                </svg>
                <span class="nav-label">統計</span>
            </button>
        </nav>
    </div>

    <!-- 創建項目模態框 -->
    <div class="modal-overlay" id="createProjectModal" style="display: none;">
        <div class="modal-content">
            <h3>創建修行項目</h3>
            <form id="createProjectForm">
                <div class="form-group">
                    <label for="projectTitle">項目名稱</label>
                    <input type="text" id="projectTitle" placeholder="例如：六字大明咒" required>
                </div>
                <div class="form-group">
                    <label for="projectGoal">目標數量</label>
                    <input type="number" id="projectGoal" placeholder="例如：1000000" min="1" required>
                </div>
                <div class="form-group">
                    <label for="projectDescription">描述（可選）</label>
                    <textarea id="projectDescription" placeholder="為此修行項目添加描述..."></textarea>
                </div>
                <div class="modal-buttons">
                    <button type="button" class="modal-btn secondary" onclick="hideCreateProject()">取消</button>
                    <button type="submit" class="modal-btn primary">創建</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 設定會期模態框 -->
    <div class="modal-overlay" id="sessionModal" style="display: none;">
        <div class="modal-content">
            <h3>開始修行會期</h3>
            <p id="sessionProjectName"></p>
            <form id="sessionForm">
                <div class="form-group">
                    <label for="sessionTarget">本次目標次數</label>
                    <input type="number" id="sessionTarget" placeholder="例如：108" min="1" required>
                </div>
                <div class="quick-targets">
                    <button type="button" class="quick-btn" onclick="setQuickTarget(108)">108</button>
                    <button type="button" class="quick-btn" onclick="setQuickTarget(1080)">1080</button>
                    <button type="button" class="quick-btn" onclick="setQuickTarget(10800)">10800</button>
                </div>
                <div class="modal-buttons">
                    <button type="button" class="modal-btn secondary" onclick="hideSessionModal()">取消</button>
                    <button type="submit" class="modal-btn primary">開始修行</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script src="js/storage.js"></script>
</body>
</html>
