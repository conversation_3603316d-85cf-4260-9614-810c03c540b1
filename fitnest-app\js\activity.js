// 活動頁面 JavaScript

class ActivityPage {
    constructor() {
        this.currentPeriod = 'week';
        this.chartData = {
            week: [
                { label: '週一', value: 6000, percentage: 60 },
                { label: '週二', value: 8000, percentage: 80 },
                { label: '週三', value: 4500, percentage: 45 },
                { label: '週四', value: 9000, percentage: 90 },
                { label: '週五', value: 7500, percentage: 75 },
                { label: '週六', value: 9500, percentage: 95 },
                { label: '今天', value: 8500, percentage: 85 }
            ],
            month: [
                { label: '第1週', value: 35000, percentage: 70 },
                { label: '第2週', value: 42000, percentage: 84 },
                { label: '第3週', value: 38000, percentage: 76 },
                { label: '第4週', value: 45000, percentage: 90 }
            ],
            year: [
                { label: '1月', value: 180000, percentage: 75 },
                { label: '2月', value: 165000, percentage: 68 },
                { label: '3月', value: 195000, percentage: 81 },
                { label: '4月', value: 210000, percentage: 87 },
                { label: '5月', value: 225000, percentage: 93 },
                { label: '6月', value: 240000, percentage: 100 }
            ]
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.animateProgressBars();
        this.animateChartBars();
        this.updateActivityData();
    }

    setupEventListeners() {
        // 返回按鈕
        const backBtn = document.querySelector('.back-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                this.goBack();
            });
        }

        // 圖表控制按鈕
        const chartBtns = document.querySelectorAll('.chart-btn');
        chartBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleChartPeriodChange(e.currentTarget);
            });
        });

        // 摘要卡片點擊
        const summaryCards = document.querySelectorAll('.summary-card');
        summaryCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.handleSummaryCardClick(e.currentTarget);
            });
        });

        // 歷史項目點擊
        const historyItems = document.querySelectorAll('.history-item');
        historyItems.forEach(item => {
            item.addEventListener('click', (e) => {
                this.handleHistoryItemClick(e.currentTarget);
            });
        });

        // 篩選按鈕
        const filterBtn = document.querySelector('.filter-btn');
        if (filterBtn) {
            filterBtn.addEventListener('click', () => {
                this.showFilterOptions();
            });
        }

        // 圖表柱狀圖點擊
        const chartBars = document.querySelectorAll('.chart-bar');
        chartBars.forEach(bar => {
            bar.addEventListener('click', (e) => {
                this.handleChartBarClick(e.currentTarget);
            });
        });
    }

    goBack() {
        // 返回首頁或上一頁
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = 'index.html';
        }
    }

    handleChartPeriodChange(button) {
        // 移除所有活動狀態
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // 添加活動狀態
        button.classList.add('active');

        // 獲取期間類型
        const period = button.textContent.toLowerCase();
        this.currentPeriod = period === '週' ? 'week' : period === '月' ? 'month' : 'year';

        // 更新圖表
        this.updateChart();

        // 添加點擊動畫
        this.addClickAnimation(button);
    }

    updateChart() {
        const chartBars = document.querySelector('.chart-bars');
        const data = this.chartData[this.currentPeriod];

        // 清空現有圖表
        chartBars.innerHTML = '';

        // 創建新的圖表柱
        data.forEach((item, index) => {
            const bar = document.createElement('div');
            bar.className = 'chart-bar';
            if (index === data.length - 1 && this.currentPeriod === 'week') {
                bar.classList.add('active');
            }
            bar.style.setProperty('--height', `${item.percentage}%`);

            bar.innerHTML = `
                <span class="bar-value">${this.formatValue(item.value)}</span>
                <div class="bar-fill"></div>
                <span class="bar-label">${item.label}</span>
            `;

            // 添加點擊事件
            bar.addEventListener('click', (e) => {
                this.handleChartBarClick(e.currentTarget);
            });

            chartBars.appendChild(bar);
        });

        // 動畫效果
        setTimeout(() => {
            this.animateChartBars();
        }, 100);
    }

    formatValue(value) {
        if (value >= 1000000) {
            return `${(value / 1000000).toFixed(1)}M`;
        } else if (value >= 1000) {
            return `${(value / 1000).toFixed(1)}k`;
        }
        return value.toString();
    }

    handleChartBarClick(bar) {
        // 移除所有活動狀態
        document.querySelectorAll('.chart-bar').forEach(b => {
            b.classList.remove('active');
        });

        // 添加活動狀態
        bar.classList.add('active');

        // 顯示詳細信息
        const label = bar.querySelector('.bar-label').textContent;
        const value = bar.querySelector('.bar-value').textContent;
        this.showBarDetails(label, value);

        // 添加點擊動畫
        this.addClickAnimation(bar);
    }

    showBarDetails(label, value) {
        const message = `${label}: ${value} 步數`;
        this.showToast(message);
    }

    handleSummaryCardClick(card) {
        const cardType = card.classList[1]; // steps, distance, calories, time
        const value = card.querySelector('.card-value').textContent;
        const label = card.querySelector('.card-label').textContent;

        this.showSummaryDetails(cardType, label, value);
        this.addClickAnimation(card);
    }

    showSummaryDetails(type, label, value) {
        let details = '';
        switch(type) {
            case 'steps':
                details = `今日步數: ${value}\n目標: 10,000 步\n完成度: 85.47%`;
                break;
            case 'distance':
                details = `今日距離: ${value} 公里\n目標: 10 公里\n完成度: 62%`;
                break;
            case 'calories':
                details = `今日消耗: ${value} 卡路里\n目標: 600 卡路里\n完成度: 70.5%`;
                break;
            case 'time':
                details = `今日運動: ${value} 分鐘\n目標: 60 分鐘\n完成度: 75%`;
                break;
        }

        this.showModal(label, details);
    }

    handleHistoryItemClick(item) {
        const title = item.querySelector('h4').textContent;
        const details = item.querySelector('p').textContent;
        const time = item.querySelector('.item-time').textContent;

        this.showWorkoutDetails(title, details, time);
        this.addClickAnimation(item);
    }

    showWorkoutDetails(title, details, time) {
        const content = `${details}\n完成時間: ${time}`;
        this.showModal(title, content);
    }

    showFilterOptions() {
        const options = ['全部', '運動', '有氧', '力量訓練', '瑜伽', '跑步'];
        const modal = this.createFilterModal(options);
        document.body.appendChild(modal);
    }

    createFilterModal(options) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content filter-modal">
                <h3>篩選運動類型</h3>
                <div class="filter-options">
                    ${options.map(option => `
                        <button class="filter-option" data-filter="${option}">${option}</button>
                    `).join('')}
                </div>
                <button class="modal-btn close-btn">關閉</button>
            </div>
        `;

        // 添加事件監聽器
        const filterOptions = modal.querySelectorAll('.filter-option');
        filterOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                this.applyFilter(e.currentTarget.dataset.filter);
                this.closeModal();
            });
        });

        const closeBtn = modal.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => this.closeModal());

        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal();
        });

        return modal;
    }

    applyFilter(filter) {
        this.showToast(`已篩選: ${filter}`);
        // 這裡可以添加實際的篩選邏輯
    }

    animateProgressBars() {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach((bar, index) => {
            setTimeout(() => {
                bar.style.width = bar.style.getPropertyValue('--progress') || '0%';
            }, index * 200);
        });
    }

    animateChartBars() {
        const chartBars = document.querySelectorAll('.chart-bar .bar-fill');
        chartBars.forEach((bar, index) => {
            setTimeout(() => {
                const height = bar.parentElement.style.getPropertyValue('--height');
                bar.style.height = height;
            }, index * 100);
        });
    }

    updateActivityData() {
        // 模擬實時數據更新
        setInterval(() => {
            this.updateStepsCount();
        }, 30000); // 每30秒更新一次
    }

    updateStepsCount() {
        const stepsCard = document.querySelector('.summary-card.steps');
        if (stepsCard) {
            const valueElement = stepsCard.querySelector('.card-value');
            const currentSteps = parseInt(valueElement.textContent.replace(',', ''));
            const newSteps = currentSteps + Math.floor(Math.random() * 50);
            
            valueElement.textContent = newSteps.toLocaleString();
            
            // 更新進度條
            const progressBar = stepsCard.querySelector('.progress-bar');
            const newProgress = (newSteps / 10000 * 100).toFixed(2);
            progressBar.style.setProperty('--progress', `${newProgress}%`);
        }
    }

    showModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>${title}</h3>
                <p style="white-space: pre-line;">${content}</p>
                <button class="modal-btn">確定</button>
            </div>
        `;

        const btn = modal.querySelector('.modal-btn');
        btn.addEventListener('click', () => this.closeModal());

        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal();
        });

        document.body.appendChild(modal);
    }

    closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    }

    showToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    addClickAnimation(element) {
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }
}

// 初始化活動頁面
document.addEventListener('DOMContentLoaded', () => {
    new ActivityPage();
});

// 導航處理
document.addEventListener('DOMContentLoaded', () => {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', (e) => {
            const label = item.querySelector('.nav-label').textContent;
            switch(label) {
                case '首頁':
                    window.location.href = 'index.html';
                    break;
                case '活動':
                    // 當前頁面，不需要跳轉
                    break;
                case '搜索':
                case '相機':
                case '個人資料':
                    // 其他頁面開發中
                    const toast = document.createElement('div');
                    toast.className = 'toast';
                    toast.textContent = `${label}頁面開發中...`;
                    document.body.appendChild(toast);
                    setTimeout(() => toast.classList.add('show'), 100);
                    setTimeout(() => {
                        toast.classList.remove('show');
                        setTimeout(() => toast.remove(), 300);
                    }, 3000);
                    break;
            }
        });
    });
});
