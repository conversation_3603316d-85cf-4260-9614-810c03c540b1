/* 咒語計數器樣式 - 基於 Fitnest 設計系統 */

/* 基礎重置和變量 */
:root {
    /* 主要顏色 */
    --primary-color: #92A3FD;
    --secondary-color: #9DCEFF;
    --accent-color: #C58BF2;
    --success-color: #42D742;
    --warning-color: #FFD600;
    --error-color: #FF6B6B;
    
    /* 中性顏色 */
    --white: #FFFFFF;
    --gray-50: #F7F8F8;
    --gray-100: #E6EAEE;
    --gray-200: #DDDADA;
    --gray-300: #ADA4A5;
    --gray-400: #7B6F72;
    --gray-500: #1D1617;
    --black: #000000;
    
    /* 漸變 */
    --gradient-primary: linear-gradient(315deg, #92A3FD 0%, #9DCEFF 100%);
    --gradient-secondary: linear-gradient(315deg, #C58BF2 0%, #EEA4CE 100%);
    --gradient-success: linear-gradient(315deg, #42D742 0%, #7ED321 100%);
    --gradient-card: linear-gradient(315deg, rgba(146, 163, 253, 0.2) 0%, rgba(157, 206, 255, 0.2) 100%);
    
    /* 字體 */
    --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    /* 間距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* 圓角 */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50%;
    
    /* 陰影 */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--white);
    color: var(--gray-500);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 應用容器 */
.app-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background-color: var(--white);
    position: relative;
    overflow-x: hidden;
}

/* 頂部導航 */
.header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
    background-color: var(--white);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-title {
    font-size: 24px;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.icon-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-50);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.icon-btn:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.icon-btn .icon {
    width: 20px;
    height: 20px;
    fill: var(--gray-400);
}

.add-btn {
    background: var(--gradient-primary);
}

.add-btn .icon {
    fill: var(--white);
}

/* 主要內容 */
.main-content {
    padding: 0 var(--spacing-lg) 100px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* 歡迎橫幅 */
.welcome-banner {
    background: var(--gradient-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.banner-content h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--gray-500);
}

.banner-content p {
    font-size: 14px;
    color: var(--gray-400);
}

.banner-graphic {
    width: 60px;
    height: 60px;
    position: relative;
}

.meditation-icon {
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 3s infinite;
}

.meditation-icon svg {
    width: 30px;
    height: 30px;
    fill: var(--white);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 今日統計 */
.today-stats h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--gray-500);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    flex-shrink: 0;
}

.stat-icon svg {
    width: 24px;
    height: 24px;
    fill: var(--white);
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--gray-500);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 12px;
    color: var(--gray-400);
    font-weight: 500;
}

/* 項目列表 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
}

.see-more-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

.projects-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.project-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.project-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.project-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-xs);
}

.project-description {
    font-size: 12px;
    color: var(--gray-400);
}

.project-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.project-icon svg {
    width: 20px;
    height: 20px;
    fill: var(--white);
}

.project-progress {
    margin-bottom: var(--spacing-md);
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.progress-current {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-500);
}

.progress-percentage {
    font-size: 12px;
    color: var(--primary-color);
    font-weight: 600;
}

.progress-bar {
    height: 6px;
    background: var(--gray-100);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    transition: width 0.5s ease;
}

/* 空狀態 */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl) var(--spacing-lg);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-card);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-icon svg {
    width: 40px;
    height: 40px;
    fill: var(--primary-color);
}

.empty-state h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    font-size: 14px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-lg);
}

.primary-btn {
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.primary-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 最近完成 */
.recent-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.recent-item {
    background: var(--white);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recent-info h5 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-xs);
}

.recent-info p {
    font-size: 12px;
    color: var(--gray-400);
}

.recent-count {
    font-size: 16px;
    font-weight: 700;
    color: var(--primary-color);
}

/* 底部導航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 375px;
    background: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 100;
}

.nav-item {
    background: none;
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    transition: all 0.2s ease;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
}

.nav-item.active {
    background: var(--gradient-card);
}

.nav-icon {
    width: 24px;
    height: 24px;
    fill: var(--gray-300);
    transition: fill 0.2s ease;
}

.nav-item.active .nav-icon {
    fill: var(--primary-color);
}

.nav-label {
    font-size: 10px;
    color: var(--gray-300);
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-item.active .nav-label {
    color: var(--primary-color);
}

/* 模態框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin: var(--spacing-lg);
    max-width: 320px;
    width: 100%;
    animation: slideUp 0.3s ease;
}

.modal-content h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

/* 表單樣式 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-500);
    margin-bottom: var(--spacing-sm);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-100);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-family: var(--font-family);
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 快速目標按鈕 */
.quick-targets {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.quick-btn {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-500);
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-btn:hover,
.quick-btn.active {
    border-color: var(--primary-color);
    background: var(--gradient-card);
    color: var(--primary-color);
}

/* 模態框按鈕 */
.modal-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.modal-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.modal-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-400);
}

.modal-btn.secondary:hover {
    background: var(--gray-200);
}

.modal-btn.primary {
    background: var(--gradient-primary);
    color: var(--white);
}

.modal-btn.primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 動畫 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 響應式設計 */
@media (max-width: 320px) {
    .app-container {
        max-width: 100%;
    }

    .header,
    .main-content {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: var(--spacing-md);
        padding: var(--spacing-lg);
    }
}

@media (min-width: 768px) {
    .app-container {
        max-width: 768px;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .modal-content {
        max-width: 400px;
    }
}
