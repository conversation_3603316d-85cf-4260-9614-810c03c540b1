# Fitnest - 健身應用 UI 套件

基於 Figma 設計文件轉換的完整健身應用前端實現。

## 📱 項目概述

這是一個現代化的健身應用 UI 套件，包含完整的用戶界面和交互功能。設計靈感來自 Pixel True 的 Fitnest 健身應用 UI 套件。

## 🎨 設計特色

- **現代化設計**: 採用漸變色彩和圓角設計
- **響應式布局**: 適配手機和平板設備
- **豐富的組件**: 包含導航、卡片、圖表、模態框等
- **流暢動畫**: CSS 動畫和 JavaScript 交互效果
- **完整功能**: 活動追蹤、運動記錄、數據可視化

## 📁 項目結構

```
fitnest-app/
├── index.html              # 主頁面
├── activity.html           # 活動頁面
├── css/
│   ├── styles.css          # 主樣式文件
│   └── activity.css        # 活動頁面樣式
├── js/
│   ├── app.js              # 主應用邏輯
│   └── activity.js         # 活動頁面邏輯
├── assets/
│   ├── icons/              # SVG 圖標
│   └── graphics/           # 圖形資源
└── README.md               # 項目說明
```

## 🚀 功能特性

### 主頁面 (index.html)
- **歡迎橫幅**: 個性化問候和活動圖表
- **今日目標**: 卡路里目標追蹤和進度環
- **狀態卡片**: 心率、水分攝取、睡眠、卡路里監控
- **最新運動**: 運動卡片和快速開始功能
- **底部導航**: 五個主要功能區域

### 活動頁面 (activity.html)
- **活動摘要**: 步數、距離、卡路里、時間統計
- **週活動圖表**: 可切換週/月/年視圖的柱狀圖
- **運動歷史**: 詳細的運動記錄列表
- **篩選功能**: 按運動類型篩選歷史記錄

## 🎯 核心組件

### 1. 導航系統
- 頂部導航欄
- 底部標籤導航
- 返回按鈕

### 2. 數據可視化
- 進度環 (Progress Ring)
- 柱狀圖 (Bar Chart)
- 進度條 (Progress Bar)

### 3. 卡片組件
- 狀態卡片
- 運動卡片
- 摘要卡片

### 4. 交互組件
- 模態框
- 提示框 (Toast)
- 按鈕動畫

## 🎨 設計系統

### 顏色方案
```css
--primary-color: #92A3FD      /* 主色調 */
--secondary-color: #9DCEFF    /* 輔助色 */
--accent-color: #C58BF2       /* 強調色 */
--success-color: #42D742      /* 成功色 */
--warning-color: #FFD600      /* 警告色 */
--error-color: #FF6B6B        /* 錯誤色 */
```

### 漸變效果
- 主要漸變: `linear-gradient(315deg, #92A3FD 0%, #9DCEFF 100%)`
- 次要漸變: `linear-gradient(315deg, #C58BF2 0%, #EEA4CE 100%)`

### 字體系統
- 主字體: Poppins
- 字重: 300, 400, 500, 600, 700

## 📱 響應式設計

### 斷點設置
- 小屏幕: `max-width: 320px`
- 中等屏幕: `375px` (默認)
- 大屏幕: `min-width: 768px`

### 適配特性
- 彈性網格布局
- 可縮放的組件
- 觸摸友好的交互區域

## 🔧 技術實現

### HTML5
- 語義化標籤
- 無障礙支持
- SEO 優化

### CSS3
- CSS 變量 (Custom Properties)
- Flexbox 和 Grid 布局
- CSS 動畫和過渡效果
- 媒體查詢

### JavaScript (ES6+)
- 類 (Class) 語法
- 模塊化設計
- 事件處理
- DOM 操作

## 🎮 交互功能

### 點擊交互
- 按鈕點擊效果
- 卡片選擇狀態
- 導航切換

### 動畫效果
- 頁面載入動畫
- 進度條動畫
- 模態框彈出動畫

### 數據更新
- 實時步數更新
- 進度條動態變化
- 圖表數據切換

## 🚀 使用方法

1. **直接打開**: 在瀏覽器中打開 `index.html`
2. **本地服務器**: 使用 Live Server 或類似工具
3. **部署**: 上傳到任何靜態網站託管服務

## 🔮 未來擴展

### 計劃功能
- [ ] 搜索頁面
- [ ] 相機功能
- [ ] 個人資料頁面
- [ ] 設置頁面
- [ ] 數據同步
- [ ] 離線支持

### 技術改進
- [ ] PWA 支持
- [ ] 暗色主題
- [ ] 多語言支持
- [ ] 性能優化

## 📄 許可證

本項目基於 MIT 許可證開源。

## 🙏 致謝

- 設計靈感: Pixel True 的 Fitnest UI 套件
- 圖標: Material Design Icons
- 字體: Google Fonts (Poppins)

---

**注意**: 這是一個前端展示項目，不包含後端功能。所有數據都是模擬數據，用於演示界面效果。
