import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import ProjectCard from '../ProjectCard';
import { StorageService } from '../../services/StorageService';
import { mockProject } from '../../test/setup';

// Mock StorageService
jest.mock('../../services/StorageService');

describe('ProjectCard', () => {
  const mockOnPress = jest.fn();
  const mockOnLongPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (StorageService.getProjectStats as jest.Mock).mockResolvedValue({
      sessionCount: 5,
      totalCount: 50000,
      totalDuration: 3600,
      avgSessionCount: 10000,
      lastSessionAt: '2024-01-01T10:00:00.000Z',
    });
  });

  it('renders project information correctly', async () => {
    const { getByText } = render(
      <ProjectCard
        project={mockProject}
        onPress={mockOnPress}
        onLongPress={mockOnLongPress}
      />
    );

    expect(getByText(mockProject.title)).toBeTruthy();
    expect(getByText(mockProject.description!)).toBeTruthy();

    await waitFor(() => {
      expect(getByText('50,000 / 1,000,000')).toBeTruthy();
      expect(getByText('5.0%')).toBeTruthy();
      expect(getByText('5 次會期')).toBeTruthy();
    });
  });

  it('calls onPress when card is pressed', () => {
    const { getByTestId } = render(
      <ProjectCard
        project={mockProject}
        onPress={mockOnPress}
        onLongPress={mockOnLongPress}
      />
    );

    // Note: You might need to add testID to the TouchableOpacity in ProjectCard
    const card = getByTestId('project-card') || getByText(mockProject.title).parent;
    fireEvent.press(card);

    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('calls onLongPress when card is long pressed', () => {
    const { getByText } = render(
      <ProjectCard
        project={mockProject}
        onPress={mockOnPress}
        onLongPress={mockOnLongPress}
      />
    );

    const card = getByText(mockProject.title).parent;
    fireEvent(card, 'longPress');

    expect(mockOnLongPress).toHaveBeenCalledTimes(1);
  });

  it('shows completed badge when project is 100% complete', async () => {
    const completedProject = {
      ...mockProject,
      currentTotalCount: 1000000,
    };

    (StorageService.getProjectStats as jest.Mock).mockResolvedValue({
      sessionCount: 10,
      totalCount: 1000000,
      totalDuration: 36000,
      avgSessionCount: 100000,
    });

    const { getByText } = render(
      <ProjectCard
        project={completedProject}
        onPress={mockOnPress}
      />
    );

    await waitFor(() => {
      expect(getByText('已完成')).toBeTruthy();
      expect(getByText('100.0%')).toBeTruthy();
    });
  });

  it('handles loading state correctly', () => {
    (StorageService.getProjectStats as jest.Mock).mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    );

    const { getByText } = render(
      <ProjectCard
        project={mockProject}
        onPress={mockOnPress}
      />
    );

    expect(getByText(mockProject.title)).toBeTruthy();
    // Should show loading state or default values
  });

  it('handles error state gracefully', async () => {
    (StorageService.getProjectStats as jest.Mock).mockRejectedValue(
      new Error('Failed to load stats')
    );

    const { getByText } = render(
      <ProjectCard
        project={mockProject}
        onPress={mockOnPress}
      />
    );

    await waitFor(() => {
      expect(getByText(mockProject.title)).toBeTruthy();
      // Should show default values when stats fail to load
    });
  });

  it('displays progress bar correctly', async () => {
    const { getByTestId } = render(
      <ProjectCard
        project={mockProject}
        onPress={mockOnPress}
      />
    );

    await waitFor(() => {
      // Note: You might need to add testID to progress elements
      const progressBar = getByTestId('progress-bar');
      const progressFill = getByTestId('progress-fill');
      
      expect(progressBar).toBeTruthy();
      expect(progressFill).toBeTruthy();
    });
  });

  it('formats large numbers correctly', async () => {
    const largeProject = {
      ...mockProject,
      totalGoalCount: 10000000,
    };

    (StorageService.getProjectStats as jest.Mock).mockResolvedValue({
      sessionCount: 100,
      totalCount: 5000000,
      totalDuration: 360000,
      avgSessionCount: 50000,
    });

    const { getByText } = render(
      <ProjectCard
        project={largeProject}
        onPress={mockOnPress}
      />
    );

    await waitFor(() => {
      expect(getByText('5,000,000 / 10,000,000')).toBeTruthy();
    });
  });

  it('shows last session date when available', async () => {
    const { getByText } = render(
      <ProjectCard
        project={mockProject}
        onPress={mockOnPress}
      />
    );

    await waitFor(() => {
      expect(getByText('今天')).toBeTruthy(); // Based on formatDate logic
    });
  });

  it('handles project without description', async () => {
    const projectWithoutDescription = {
      ...mockProject,
      description: undefined,
    };

    const { queryByText } = render(
      <ProjectCard
        project={projectWithoutDescription}
        onPress={mockOnPress}
      />
    );

    expect(queryByText(mockProject.description!)).toBeFalsy();
  });

  it('applies correct styling based on completion status', async () => {
    const { getByTestId } = render(
      <ProjectCard
        project={mockProject}
        onPress={mockOnPress}
      />
    );

    await waitFor(() => {
      const progressFill = getByTestId('progress-fill');
      // Check if the progress fill has the correct color based on percentage
      expect(progressFill.props.style).toMatchObject({
        backgroundColor: expect.any(String),
      });
    });
  });
});
