import 'react-native-gesture-handler/jestSetup';
import '@testing-library/jest-native/extend-expect';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

// Mock React Navigation
jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useNavigation: () => ({
      navigate: jest.fn(),
      goBack: jest.fn(),
      replace: jest.fn(),
      push: jest.fn(),
      pop: jest.fn(),
      popToTop: jest.fn(),
      reset: jest.fn(),
    }),
    useRoute: () => ({
      params: {},
    }),
    useFocusEffect: jest.fn(),
  };
});

// Mock Expo modules
jest.mock('expo-av', () => ({
  Audio: {
    setAudioModeAsync: jest.fn(),
    Sound: {
      createAsync: jest.fn(() => Promise.resolve({
        sound: {
          setVolumeAsync: jest.fn(),
          replayAsync: jest.fn(),
          unloadAsync: jest.fn(),
          setOnPlaybackStatusUpdate: jest.fn(),
        }
      })),
    },
  },
}));

jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  notificationAsync: jest.fn(),
  isAvailableAsync: jest.fn(() => Promise.resolve(true)),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy',
  },
  NotificationFeedbackType: {
    Success: 'success',
    Warning: 'warning',
    Error: 'error',
  },
}));

jest.mock('expo-linear-gradient', () => ({
  LinearGradient: 'LinearGradient',
}));

jest.mock('expo-sharing', () => ({
  shareAsync: jest.fn(),
  isAvailableAsync: jest.fn(() => Promise.resolve(true)),
}));

jest.mock('react-native-svg', () => ({
  Svg: 'Svg',
  Circle: 'Circle',
  Path: 'Path',
  G: 'G',
}));

// Mock Ionicons
jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons',
}));

// Mock React Native components
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Alert: {
      alert: jest.fn(),
      prompt: jest.fn(),
    },
    Share: {
      share: jest.fn(),
    },
    BackHandler: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    Dimensions: {
      get: jest.fn(() => ({ width: 375, height: 812 })),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
  };
});

// Global test utilities
global.console = {
  ...console,
  // Suppress console.error during tests unless it's a real error
  error: jest.fn(),
  warn: jest.fn(),
};

// Mock timers
jest.useFakeTimers();

// Setup test data
export const mockProject = {
  id: 'test-project-1',
  title: '六字大明咒',
  description: '觀音菩薩心咒',
  totalGoalCount: 1000000,
  currentTotalCount: 50000,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
};

export const mockSession = {
  id: 'test-session-1',
  projectId: 'test-project-1',
  count: 108,
  target: 108,
  duration: 600,
  startTime: '2024-01-01T10:00:00.000Z',
  endTime: '2024-01-01T10:10:00.000Z',
  completed: true,
  createdAt: '2024-01-01T10:10:00.000Z',
};

export const mockWish = {
  id: 'test-wish-1',
  text: '願家人身體健康',
  category: 'health' as const,
  blessedCount: 3,
  lastBlessedAt: '2024-01-01T12:00:00.000Z',
  createdAt: '2024-01-01T08:00:00.000Z',
};

export const mockBlessing = {
  id: 'test-blessing-1',
  projectId: 'test-project-1',
  count: 108,
  wishIds: ['test-wish-1'],
  dedicationText: '願以此功德，迴向法界眾生',
  sessionData: mockSession,
  createdAt: '2024-01-01T10:15:00.000Z',
};
