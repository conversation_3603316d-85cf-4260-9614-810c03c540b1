describe('咒語計數器 E2E 測試', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('歡迎流程', () => {
    it('應該顯示歡迎頁面並能夠完成引導', async () => {
      // 檢查歡迎頁面元素
      await expect(element(by.text('咒語計數器'))).toBeVisible();
      await expect(element(by.text('現代化修行助手'))).toBeVisible();
      await expect(element(by.text('開始修行之旅'))).toBeVisible();

      // 點擊開始按鈕
      await element(by.text('開始修行之旅')).tap();

      // 應該進入主頁面
      await expect(element(by.text('開始今日修行'))).toBeVisible();
    });
  });

  describe('項目管理', () => {
    it('應該能夠創建新的修行項目', async () => {
      // 跳過歡迎頁面
      if (await element(by.text('開始修行之旅')).isVisible()) {
        await element(by.text('開始修行之旅')).tap();
      }

      // 點擊添加按鈕
      await element(by.id('add-project-button')).tap();

      // 填寫項目信息
      await element(by.id('project-title-input')).typeText('六字大明咒');
      await element(by.id('project-goal-input')).typeText('1000000');
      await element(by.id('project-description-input')).typeText('觀音菩薩心咒');

      // 提交創建
      await element(by.text('創建')).tap();

      // 驗證項目已創建
      await expect(element(by.text('六字大明咒'))).toBeVisible();
      await expect(element(by.text('觀音菩薩心咒'))).toBeVisible();
    });

    it('應該顯示項目進度', async () => {
      // 確保有項目存在
      await expect(element(by.text('六字大明咒'))).toBeVisible();
      
      // 檢查進度顯示
      await expect(element(by.text('0 / 1,000,000'))).toBeVisible();
      await expect(element(by.text('0.0%'))).toBeVisible();
    });
  });

  describe('計數功能', () => {
    it('應該能夠開始修行會期並進行計數', async () => {
      // 點擊項目卡片
      await element(by.text('六字大明咒')).tap();

      // 設定目標
      await element(by.text('開始')).tap();
      
      // 應該進入計數頁面
      await expect(element(by.text('六字大明咒'))).toBeVisible();
      await expect(element(by.text('0'))).toBeVisible();
      await expect(element(by.text('/ 108'))).toBeVisible();

      // 進行計數
      const counterButton = element(by.id('counter-button'));
      await counterButton.tap();
      await counterButton.tap();
      await counterButton.tap();

      // 驗證計數增加
      await expect(element(by.text('3'))).toBeVisible();
    });

    it('應該能夠使用快速操作', async () => {
      // 使用 +10 按鈕
      await element(by.text('+10')).tap();
      await expect(element(by.text('13'))).toBeVisible();

      // 使用 -1 按鈕
      await element(by.text('-1')).tap();
      await expect(element(by.text('12'))).toBeVisible();
    });

    it('應該能夠暫停和恢復計數', async () => {
      // 點擊暫停按鈕
      await element(by.id('pause-button')).tap();
      
      // 驗證暫停狀態
      await expect(element(by.text('已暫停'))).toBeVisible();

      // 恢復計數
      await element(by.id('pause-button')).tap();
      await expect(element(by.text('點擊持誦'))).toBeVisible();
    });
  });

  describe('願望清單', () => {
    it('應該能夠添加新願望', async () => {
      // 導航到願望清單
      await element(by.text('願望清單')).tap();

      // 點擊添加按鈕
      await element(by.id('add-wish-button')).tap();

      // 填寫願望內容
      await element(by.id('wish-text-input')).typeText('願家人身體健康');
      
      // 選擇分類
      await element(by.text('健康')).tap();

      // 提交創建
      await element(by.text('添加')).tap();

      // 驗證願望已添加
      await expect(element(by.text('願家人身體健康'))).toBeVisible();
      await expect(element(by.text('健康'))).toBeVisible();
    });

    it('應該能夠篩選願望', async () => {
      // 點擊篩選按鈕
      await element(by.text('待祝福')).tap();
      
      // 應該顯示未祝福的願望
      await expect(element(by.text('願家人身體健康'))).toBeVisible();
      await expect(element(by.text('待祝福'))).toBeVisible();
    });
  });

  describe('功德迴向', () => {
    it('應該能夠完成修行並進行迴向', async () => {
      // 返回計數頁面
      await element(by.text('首頁')).tap();
      await element(by.text('六字大明咒')).tap();
      await element(by.text('開始')).tap();

      // 完成目標計數
      const counterButton = element(by.id('counter-button'));
      for (let i = 0; i < 108; i++) {
        await counterButton.tap();
      }

      // 應該彈出完成提示
      await expect(element(by.text('修行完成！'))).toBeVisible();
      
      // 選擇迴向功德
      await element(by.text('迴向功德')).tap();

      // 應該進入迴向頁面
      await expect(element(by.text('功德迴向'))).toBeVisible();
      await expect(element(by.text('修行圓滿'))).toBeVisible();
    });

    it('應該能夠編輯迴向文', async () => {
      // 編輯迴向文
      const dedicationInput = element(by.id('dedication-input'));
      await dedicationInput.clearText();
      await dedicationInput.typeText('願以此功德，迴向法界眾生，願眾生離苦得樂。');

      // 驗證文字已輸入
      await expect(element(by.text('願以此功德，迴向法界眾生，願眾生離苦得樂。'))).toBeVisible();
    });

    it('應該能夠選擇願望進行灌注', async () => {
      // 選擇願望
      await element(by.text('願家人身體健康')).tap();
      
      // 驗證選中狀態
      await expect(element(by.id('selected-badge'))).toBeVisible();

      // 點擊灌注按鈕
      await element(by.text('灌注到願望清單')).tap();

      // 應該顯示成功提示
      await expect(element(by.text('功德灌注成功'))).toBeVisible();
    });
  });

  describe('數據持久化', () => {
    it('應該在重啟應用後保持數據', async () => {
      // 重啟應用
      await device.launchApp({ delete: false });

      // 跳過歡迎頁面（如果是首次啟動）
      if (await element(by.text('開始修行之旅')).isVisible()) {
        await element(by.text('開始修行之旅')).tap();
      }

      // 驗證項目仍然存在
      await expect(element(by.text('六字大明咒'))).toBeVisible();

      // 檢查願望清單
      await element(by.text('願望清單')).tap();
      await expect(element(by.text('願家人身體健康'))).toBeVisible();
      await expect(element(by.text('已祝福 1 次'))).toBeVisible();
    });
  });

  describe('錯誤處理', () => {
    it('應該處理無效輸入', async () => {
      // 嘗試創建空項目
      await element(by.text('首頁')).tap();
      await element(by.id('add-project-button')).tap();
      await element(by.text('創建')).tap();

      // 應該顯示錯誤提示
      await expect(element(by.text('請輸入項目名稱'))).toBeVisible();
    });

    it('應該處理網絡錯誤', async () => {
      // 模擬網絡錯誤情況
      await device.setURLBlacklist(['.*']);
      
      // 嘗試分享功能
      await element(by.text('分享到社群')).tap();
      
      // 應該顯示錯誤處理
      await expect(element(by.text('分享失敗，請重試'))).toBeVisible();
      
      // 恢復網絡
      await device.setURLBlacklist([]);
    });
  });

  describe('性能測試', () => {
    it('應該能夠處理大量計數', async () => {
      // 快速計數測試
      await element(by.text('首頁')).tap();
      await element(by.text('六字大明咒')).tap();
      await element(by.text('開始')).tap();

      const counterButton = element(by.id('counter-button'));
      const startTime = Date.now();
      
      // 快速點擊 100 次
      for (let i = 0; i < 100; i++) {
        await counterButton.tap();
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 驗證性能（應該在合理時間內完成）
      expect(duration).toBeLessThan(30000); // 30 秒內完成
      await expect(element(by.text('100'))).toBeVisible();
    });
  });
});
