import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';

import { Colors, ColorUtils } from '../constants/Colors';
import { StorageService } from '../services/StorageService';
import { AudioService } from '../services/AudioService';
import { Wish, WishCategory } from '../types';

// Components
import StatsCard from '../components/StatsCard';
import CreateWishModal from '../components/CreateWishModal';

const WishListScreen: React.FC = () => {
  const [wishes, setWishes] = useState<Wish[]>([]);
  const [filteredWishes, setFilteredWishes] = useState<Wish[]>([]);
  const [currentFilter, setCurrentFilter] = useState<'all' | 'blessed' | 'pending'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // 加載數據
  const loadData = async () => {
    try {
      const wishesData = await StorageService.getWishes();
      setWishes(wishesData);
      filterWishes(wishesData, currentFilter);
    } catch (error) {
      console.error('Failed to load wishes:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // 頁面聚焦時重新加載數據
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [])
  );

  // 篩選願望
  const filterWishes = (wishList: Wish[], filter: typeof currentFilter) => {
    let filtered = wishList;
    
    switch (filter) {
      case 'blessed':
        filtered = wishList.filter(w => w.blessedCount > 0);
        break;
      case 'pending':
        filtered = wishList.filter(w => w.blessedCount === 0);
        break;
      default:
        filtered = wishList;
    }
    
    setFilteredWishes(filtered);
  };

  // 下拉刷新
  const onRefresh = () => {
    setIsRefreshing(true);
    loadData();
  };

  // 設置篩選器
  const handleFilterChange = async (filter: typeof currentFilter) => {
    await AudioService.buttonPress();
    setCurrentFilter(filter);
    filterWishes(wishes, filter);
  };

  // 創建願望
  const handleCreateWish = async (wishData: {
    text: string;
    category?: WishCategory;
  }) => {
    try {
      const newWish = await StorageService.addWish(wishData);
      
      if (newWish) {
        await AudioService.buttonSuccess();
        setWishes(prev => [...prev, newWish]);
        filterWishes([...wishes, newWish], currentFilter);
        setShowCreateModal(false);
      } else {
        await AudioService.buttonError();
        Alert.alert('錯誤', '創建願望失敗，請重試');
      }
    } catch (error) {
      console.error('Failed to create wish:', error);
      await AudioService.buttonError();
      Alert.alert('錯誤', '創建願望失敗，請重試');
    }
  };

  // 刪除願望
  const handleDeleteWish = async (wishId: string) => {
    Alert.alert(
      '刪除願望',
      '確定要刪除這個願望嗎？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '刪除',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await StorageService.deleteWish(wishId);
              if (success) {
                await AudioService.buttonSuccess();
                const updatedWishes = wishes.filter(w => w.id !== wishId);
                setWishes(updatedWishes);
                filterWishes(updatedWishes, currentFilter);
              } else {
                await AudioService.buttonError();
                Alert.alert('錯誤', '刪除失敗，請重試');
              }
            } catch (error) {
              console.error('Failed to delete wish:', error);
              await AudioService.buttonError();
              Alert.alert('錯誤', '刪除失敗，請重試');
            }
          },
        },
      ]
    );
  };

  // 獲取統計數據
  const getStats = () => {
    const totalWishes = wishes.length;
    const blessedWishes = wishes.filter(w => w.blessedCount > 0).length;
    return { totalWishes, blessedWishes };
  };

  // 獲取分類名稱
  const getCategoryName = (category?: WishCategory) => {
    const categories = {
      health: '健康',
      family: '家庭',
      career: '事業',
      study: '學業',
      peace: '平安',
      other: '其他',
    };
    return category ? categories[category] : '';
  };

  const { totalWishes, blessedWishes } = getStats();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* 頂部導航 */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>願望清單</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowCreateModal(true)}
          >
            <LinearGradient
              colors={Colors.gradientPrimary}
              style={styles.addButtonGradient}
            >
              <Ionicons name="add" size={24} color={Colors.white} />
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* 統計卡片 */}
        <View style={styles.statsSection}>
          <View style={styles.statsGrid}>
            <StatsCard
              icon="heart-outline"
              title="總願望數"
              value={totalWishes.toString()}
              color={Colors.accent}
            />
            <StatsCard
              icon="star-outline"
              title="已祝福"
              value={blessedWishes.toString()}
              color={Colors.success}
            />
          </View>
        </View>

        {/* 篩選按鈕 */}
        <View style={styles.filterSection}>
          <View style={styles.filterButtons}>
            {[
              { key: 'all', label: '全部' },
              { key: 'blessed', label: '已祝福' },
              { key: 'pending', label: '待祝福' },
            ].map((filter) => (
              <TouchableOpacity
                key={filter.key}
                style={[
                  styles.filterButton,
                  currentFilter === filter.key && styles.filterButtonActive,
                ]}
                onPress={() => handleFilterChange(filter.key as typeof currentFilter)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    currentFilter === filter.key && styles.filterButtonTextActive,
                  ]}
                >
                  {filter.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 願望列表 */}
        <View style={styles.wishesSection}>
          {filteredWishes.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons
                name="heart-outline"
                size={64}
                color={Colors.gray300}
                style={styles.emptyIcon}
              />
              <Text style={styles.emptyTitle}>
                {currentFilter === 'all' 
                  ? '還沒有願望' 
                  : currentFilter === 'blessed'
                  ? '還沒有已祝福的願望'
                  : '還沒有待祝福的願望'
                }
              </Text>
              <Text style={styles.emptyDescription}>
                {currentFilter === 'all' 
                  ? '點擊右上角的 + 號添加您的第一個願望'
                  : '完成修行後可以將功德灌注到願望中'
                }
              </Text>
              {currentFilter === 'all' && (
                <TouchableOpacity
                  style={styles.emptyButton}
                  onPress={() => setShowCreateModal(true)}
                >
                  <Text style={styles.emptyButtonText}>添加願望</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <View style={styles.wishesList}>
              {filteredWishes.map((wish) => (
                <WishItem
                  key={wish.id}
                  wish={wish}
                  onDelete={() => handleDeleteWish(wish.id)}
                  getCategoryName={getCategoryName}
                />
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* 創建願望模態框 */}
      <CreateWishModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateWish}
      />
    </SafeAreaView>
  );
};

// 願望項目組件
interface WishItemProps {
  wish: Wish;
  onDelete: () => void;
  getCategoryName: (category?: WishCategory) => string;
}

const WishItem: React.FC<WishItemProps> = ({ wish, onDelete, getCategoryName }) => {
  return (
    <View style={[styles.wishItem, wish.blessedCount > 0 && styles.wishItemBlessed]}>
      {/* 願望內容 */}
      <View style={styles.wishContent}>
        {wish.category && (
          <View style={[styles.categoryTag, { backgroundColor: ColorUtils.getWishCategoryColor(wish.category) + '20' }]}>
            <Text style={[styles.categoryText, { color: ColorUtils.getWishCategoryColor(wish.category) }]}>
              {getCategoryName(wish.category)}
            </Text>
          </View>
        )}
        
        <Text style={styles.wishText}>{wish.text}</Text>
        
        <View style={styles.wishFooter}>
          <Text style={styles.wishDate}>
            {StorageService.formatDate(wish.createdAt)}
          </Text>
          
          {wish.blessedCount > 0 ? (
            <Text style={styles.blessedCount}>
              已祝福 {wish.blessedCount} 次
            </Text>
          ) : (
            <Text style={styles.pendingText}>待祝福</Text>
          )}
        </View>
      </View>

      {/* 操作按鈕 */}
      <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
        <Ionicons name="trash-outline" size={16} color={Colors.error} />
      </TouchableOpacity>

      {/* 已祝福標記 */}
      {wish.blessedCount > 0 && (
        <View style={styles.blessedBadge}>
          <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.textPrimary,
  },
  addButton: {
    width: 48,
    height: 48,
  },
  addButtonGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  filterSection: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.backgroundSecondary,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary,
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  filterButtonTextActive: {
    color: Colors.white,
  },
  wishesSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  wishesList: {
    gap: 16,
  },
  wishItem: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 20,
    position: 'relative',
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  wishItemBlessed: {
    borderLeftWidth: 4,
    borderLeftColor: Colors.success,
  },
  wishContent: {
    flex: 1,
    marginRight: 40,
  },
  categoryTag: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  wishText: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textPrimary,
    lineHeight: 20,
    marginBottom: 12,
  },
  wishFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  wishDate: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textTertiary,
  },
  blessedCount: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.success,
  },
  pendingText: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textSecondary,
  },
  deleteButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  blessedBadge: {
    position: 'absolute',
    top: 16,
    right: 56,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 32,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  emptyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
  },
});

export default WishListScreen;
