// 主應用邏輯

class MantraCounterApp {
    constructor() {
        this.currentProject = null;
        this.init();
    }

    init() {
        this.updateGreeting();
        this.loadTodayStats();
        this.loadProjects();
        this.loadRecentSessions();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 創建項目表單
        const createForm = document.getElementById('createProjectForm');
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createProject();
            });
        }

        // 會期設定表單
        const sessionForm = document.getElementById('sessionForm');
        if (sessionForm) {
            sessionForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.startSession();
            });
        }

        // 點擊項目卡片
        document.addEventListener('click', (e) => {
            const projectCard = e.target.closest('.project-card');
            if (projectCard) {
                const projectId = projectCard.dataset.projectId;
                this.selectProject(projectId);
            }
        });
    }

    updateGreeting() {
        const greetingElement = document.getElementById('greeting');
        if (greetingElement) {
            const hour = new Date().getHours();
            let greeting = '開始今日修行';
            
            if (hour < 6) {
                greeting = '夜深了，願您安眠';
            } else if (hour < 12) {
                greeting = '早安，開始今日修行';
            } else if (hour < 18) {
                greeting = '午安，繼續精進修行';
            } else {
                greeting = '晚安，圓滿今日修行';
            }
            
            greetingElement.textContent = greeting;
        }
    }

    loadTodayStats() {
        const stats = storage.getTodayStats();
        
        const todaySessionsElement = document.getElementById('todaySessions');
        const todayCountElement = document.getElementById('todayCount');
        
        if (todaySessionsElement) {
            todaySessionsElement.textContent = stats.sessionCount;
        }
        
        if (todayCountElement) {
            todayCountElement.textContent = stats.totalCount.toLocaleString();
        }
    }

    loadProjects() {
        const projects = storage.getProjects();
        const projectsList = document.getElementById('projectsList');
        const emptyState = document.getElementById('emptyState');
        
        if (!projectsList) return;

        if (projects.length === 0) {
            projectsList.style.display = 'none';
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        if (emptyState) emptyState.style.display = 'none';
        projectsList.style.display = 'flex';
        projectsList.innerHTML = '';

        projects.forEach(project => {
            const projectCard = this.createProjectCard(project);
            projectsList.appendChild(projectCard);
        });
    }

    createProjectCard(project) {
        const stats = storage.getProjectStats(project.id);
        const percentage = project.totalGoalCount > 0 
            ? Math.min((stats.totalCount / project.totalGoalCount) * 100, 100)
            : 0;

        const card = document.createElement('div');
        card.className = 'project-card';
        card.dataset.projectId = project.id;
        
        card.innerHTML = `
            <div class="project-header">
                <div class="project-info">
                    <h4 class="project-title">${project.title}</h4>
                    <p class="project-description">${project.description || '願此修行功德無量'}</p>
                </div>
                <div class="project-icon">
                    <svg viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
                    </svg>
                </div>
            </div>
            <div class="project-progress">
                <div class="progress-text">
                    <span class="progress-current">${stats.totalCount.toLocaleString()} / ${project.totalGoalCount.toLocaleString()}</span>
                    <span class="progress-percentage">${percentage.toFixed(1)}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${percentage}%"></div>
                </div>
            </div>
        `;

        return card;
    }

    loadRecentSessions() {
        const sessions = storage.getRecentSessions(3);
        const recentList = document.getElementById('recentList');
        
        if (!recentList) return;

        if (sessions.length === 0) {
            recentList.innerHTML = '<p style="text-align: center; color: var(--gray-400); font-size: 14px;">還沒有修行記錄</p>';
            return;
        }

        recentList.innerHTML = '';
        sessions.forEach(session => {
            const project = storage.getProject(session.projectId);
            if (project) {
                const item = this.createRecentItem(session, project);
                recentList.appendChild(item);
            }
        });
    }

    createRecentItem(session, project) {
        const item = document.createElement('div');
        item.className = 'recent-item';
        
        const timeAgo = storage.formatDate(session.createdAt);
        const duration = session.duration ? storage.formatDuration(session.duration) : '未知';
        
        item.innerHTML = `
            <div class="recent-info">
                <h5>${project.title}</h5>
                <p>${timeAgo} • ${duration}</p>
            </div>
            <div class="recent-count">${session.count}</div>
        `;

        return item;
    }

    selectProject(projectId) {
        this.currentProject = storage.getProject(projectId);
        if (this.currentProject) {
            this.showSessionModal();
        }
    }

    showSessionModal() {
        const modal = document.getElementById('sessionModal');
        const projectName = document.getElementById('sessionProjectName');
        
        if (modal && projectName && this.currentProject) {
            projectName.textContent = `開始修行：${this.currentProject.title}`;
            modal.style.display = 'flex';
        }
    }

    hideSessionModal() {
        const modal = document.getElementById('sessionModal');
        if (modal) {
            modal.style.display = 'none';
        }
        this.currentProject = null;
    }

    startSession() {
        const targetInput = document.getElementById('sessionTarget');
        const target = parseInt(targetInput.value);
        
        if (!target || target <= 0) {
            this.showToast('請輸入有效的目標次數');
            return;
        }

        if (!this.currentProject) {
            this.showToast('請先選擇修行項目');
            return;
        }

        // 保存會期信息到 sessionStorage
        const sessionData = {
            projectId: this.currentProject.id,
            projectTitle: this.currentProject.title,
            target: target,
            startTime: new Date().toISOString()
        };
        
        sessionStorage.setItem('currentSession', JSON.stringify(sessionData));
        
        // 跳轉到計數頁面
        window.location.href = 'counter.html';
    }

    createProject() {
        const titleInput = document.getElementById('projectTitle');
        const goalInput = document.getElementById('projectGoal');
        const descriptionInput = document.getElementById('projectDescription');
        
        const title = titleInput.value.trim();
        const goal = parseInt(goalInput.value);
        const description = descriptionInput.value.trim();
        
        if (!title) {
            this.showToast('請輸入項目名稱');
            return;
        }
        
        if (!goal || goal <= 0) {
            this.showToast('請輸入有效的目標數量');
            return;
        }

        const project = {
            title,
            totalGoalCount: goal,
            description,
            currentTotalCount: 0
        };

        if (storage.addProject(project)) {
            this.showToast('項目創建成功！');
            this.hideCreateProject();
            this.loadProjects();
            
            // 清空表單
            titleInput.value = '';
            goalInput.value = '';
            descriptionInput.value = '';
        } else {
            this.showToast('創建失敗，請重試');
        }
    }

    showCreateProject() {
        const modal = document.getElementById('createProjectModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    hideCreateProject() {
        const modal = document.getElementById('createProjectModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    setQuickTarget(value) {
        const targetInput = document.getElementById('sessionTarget');
        if (targetInput) {
            targetInput.value = value;
        }
        
        // 更新快速按鈕狀態
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    showToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    // 導航方法
    showWishList() {
        window.location.href = 'wishlist.html';
    }

    showHistory() {
        this.showToast('歷史頁面開發中...');
    }

    showStats() {
        this.showToast('統計頁面開發中...');
    }

    showAllProjects() {
        this.showToast('查看全部項目功能開發中...');
    }
}

// 全局函數
function showCreateProject() {
    app.showCreateProject();
}

function hideCreateProject() {
    app.hideCreateProject();
}

function hideSessionModal() {
    app.hideSessionModal();
}

function setQuickTarget(value) {
    app.setQuickTarget(value);
}

function showWishList() {
    app.showWishList();
}

function showHistory() {
    app.showHistory();
}

function showStats() {
    app.showStats();
}

function showAllProjects() {
    app.showAllProjects();
}

// 初始化應用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new MantraCounterApp();
});

// 工具函數
window.MantraUtils = {
    showToast: function(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    },

    playSound: function(type = 'success') {
        const settings = storage.getSettings();
        if (!settings.soundEnabled) return;

        // 創建音頻上下文
        if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
            const audioContext = new (AudioContext || webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            if (type === 'success') {
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
            } else if (type === 'click') {
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
            }

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }
    },

    vibrate: function(pattern = [100]) {
        const settings = storage.getSettings();
        if (!settings.vibrationEnabled) return;

        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    }
};

// 添加 CSS 樣式到頁面
const style = document.createElement('style');
style.textContent = `
    .toast {
        position: fixed;
        bottom: 120px;
        left: 50%;
        transform: translateX(-50%) translateY(100px);
        background: var(--gray-500);
        color: var(--white);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-md);
        font-size: 14px;
        font-weight: 500;
        z-index: 1001;
        opacity: 0;
        transition: all 0.3s ease;
        max-width: 280px;
        text-align: center;
    }

    .toast.show {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
`;
document.head.appendChild(style);
