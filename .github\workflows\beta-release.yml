# Beta Release Workflow
# Triggers when the beta branch is pushed and publishes packages with a -beta tag

name: Beta Release

on:
  push:
    branches:
      - beta

jobs:
  beta-release:
    if: ${{ github.repository_owner == 'GLips' }}
    name: Create a beta release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup

      - name: Check for errors
        run: pnpm type-check

      - name: Build the package
        run: pnpm build

      - name: Create Version and Publish Beta
        id: changesets
        uses: changesets/action@v1
        with:
          commit: "chore(beta): version packages"
          title: "chore(beta): version packages"
          version: node .github/changeset-beta-version.js
          publish: npx changeset publish
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_ACCESS_TOKEN }}
          NODE_ENV: "production"
