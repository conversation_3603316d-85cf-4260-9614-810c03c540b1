# 🚀 咒語計數器 - 瀏覽和測試指南

本指南將引導您完成咒語計數器應用的瀏覽和測試，包括 Web 版本和 React Native 版本。

## 📋 快速開始

### 🌐 Web 版本測試（最快方式）

**立即體驗：**
1. 打開瀏覽器
2. 訪問：`file:///E:/wamp8/www/figma/Figma-Context-MCP/counter-app/start.html`
3. 或者雙擊 `counter-app/start.html` 文件

**頁面導航：**
- **啟動頁面** (`start.html`) - 功能介紹和導航
- **主頁面** (`index.html`) - 項目管理和統計
- **計數頁面** (`counter.html`) - 極簡計數界面  
- **願望清單** (`wishlist.html`) - 願望管理
- **功德迴向** (`merit.html`) - 迴向分享

### 📱 React Native 版本測試

#### 方法一：Expo 開發服務器（推薦）

```bash
# 1. 進入 React Native 目錄
cd counter-app/react-native

# 2. 安裝依賴
npm install

# 3. 啟動開發服務器
npm start
```

**然後選擇：**
- **手機測試**: 下載 Expo Go App，掃描 QR 碼
- **iOS 模擬器**: 按 `i` 鍵
- **Android 模擬器**: 按 `a` 鍵
- **Web 瀏覽器**: 按 `w` 鍵

#### 方法二：本地構建測試

```bash
# iOS 模擬器
npm run ios

# Android 模擬器  
npm run android
```

## 📋 完整測試清單

### 🎯 核心功能測試

#### 1. 項目管理
- [ ] **創建項目**
  - 點擊右上角 "+" 按鈕
  - 輸入項目名稱：六字大明咒
  - 設定目標數量：1000000
  - 添加描述：觀音菩薩心咒
  - 點擊創建

- [ ] **查看項目**
  - 檢查項目卡片顯示
  - 確認進度條和百分比
  - 查看統計信息

- [ ] **編輯項目**
  - 長按項目卡片
  - 修改項目信息
  - 保存更改

#### 2. 計數功能
- [ ] **開始修行**
  - 點擊項目卡片
  - 設定會期目標：108
  - 進入計數頁面

- [ ] **基本計數**
  - 點擊中央計數按鈕
  - 觀察數字增加
  - 檢查進度環更新

- [ ] **快速操作**
  - 測試 "+10" 按鈕
  - 測試 "-1" 按鈕
  - 測試重置按鈕

- [ ] **暫停恢復**
  - 點擊暫停按鈕
  - 確認計數停止
  - 點擊恢復計數

- [ ] **完成會期**
  - 計數到目標數量
  - 查看完成提示
  - 選擇迴向功德

#### 3. 願望清單
- [ ] **添加願望**
  - 進入願望清單頁面
  - 點擊 "+" 按鈕
  - 輸入願望：願家人身體健康
  - 選擇分類：健康
  - 提交創建

- [ ] **管理願望**
  - 查看願望列表
  - 編輯願望內容
  - 刪除不需要的願望

- [ ] **篩選功能**
  - 點擊 "全部" 篩選
  - 點擊 "已祝福" 篩選
  - 點擊 "待祝福" 篩選

#### 4. 功德迴向
- [ ] **編輯迴向文**
  - 修改默認迴向文
  - 使用預設模板
  - 自定義迴向內容

- [ ] **選擇願望**
  - 勾選要灌注的願望
  - 確認選中狀態
  - 取消選擇

- [ ] **功德灌注**
  - 點擊 "灌注到願望清單"
  - 查看成功提示
  - 確認願望祝福計數增加

- [ ] **分享功能**
  - 點擊 "分享到社群"
  - 測試分享功能

### 🎨 用戶體驗測試

#### 1. 界面設計
- [ ] **視覺一致性**
  - 檢查色彩搭配
  - 確認字體統一
  - 驗證間距規範

- [ ] **響應式設計**
  - 調整瀏覽器窗口大小
  - 測試不同屏幕尺寸
  - 檢查移動端適配

#### 2. 交互體驗
- [ ] **動畫效果**
  - 觀察頁面切換動畫
  - 檢查按鈕點擊效果
  - 測試進度環動畫

- [ ] **音效反饋**（Web版本）
  - 測試按鈕點擊音效
  - 檢查完成提示音效
  - 驗證錯誤提示音效

- [ ] **觸覺反饋**（手機版本）
  - 測試按鈕震動反饋
  - 檢查計數震動
  - 驗證完成震動

#### 3. 導航流程
- [ ] **頁面切換**
  - 測試底部導航
  - 檢查返回按鈕
  - 驗證頁面跳轉

- [ ] **狀態保持**
  - 切換頁面後返回
  - 檢查數據是否保持
  - 驗證輸入狀態

### 💾 數據測試

#### 1. 數據持久化
- [ ] **本地存儲**
  - 創建項目後刷新頁面
  - 檢查數據是否保存
  - 驗證統計數據正確

- [ ] **數據恢復**
  - 關閉應用重新打開
  - 確認所有數據恢復
  - 檢查歷史記錄

#### 2. 數據同步
- [ ] **實時更新**
  - 修改項目信息
  - 檢查相關頁面更新
  - 驗證統計數據同步

### 🚨 錯誤處理測試

#### 1. 輸入驗證
- [ ] **空值處理**
  - 嘗試創建空項目
  - 檢查錯誤提示
  - 確認無法提交

- [ ] **無效輸入**
  - 輸入負數目標
  - 輸入過大數字
  - 檢查驗證邏輯

#### 2. 邊界情況
- [ ] **極限測試**
  - 創建大量項目
  - 進行大量計數
  - 添加大量願望

- [ ] **異常操作**
  - 快速連續點擊
  - 同時操作多個功能
  - 測試併發處理

## 🧪 自動化測試

### 快速測試（5 分鐘）
```bash
cd counter-app/react-native

# 基本檢查
npm run type-check    # TypeScript 類型檢查
npm run lint         # 代碼規範檢查  
npm test            # 單元測試
```

### 完整測試套件（45 分鐘）
```bash
# 賦予執行權限
chmod +x scripts/test.sh

# 運行完整測試
./scripts/test.sh
```

**測試包含：**
- 代碼質量檢查
- 單元測試和覆蓋率
- 構建測試
- 性能測試
- 安全檢查
- 商店準備檢查

### E2E 自動化測試
```bash
# 構建測試應用
npm run test:e2e:build

# 運行端到端測試
npm run test:e2e
```

## 📊 測試結果評估

### ✅ 成功標準

#### 功能完整性
- [ ] 所有核心功能正常工作
- [ ] 用戶流程完整無阻斷
- [ ] 數據存儲和恢復正常
- [ ] 錯誤處理完善

#### 性能表現
- [ ] 頁面加載時間 < 2 秒
- [ ] 操作響應時間 < 300ms
- [ ] 動畫流暢度 60fps
- [ ] 內存使用合理

#### 用戶體驗
- [ ] 界面美觀一致
- [ ] 交互直觀自然
- [ ] 反饋及時明確
- [ ] 導航流暢順暢

### 📱 設備兼容性

#### Web 版本
- [ ] Chrome 90+
- [ ] Firefox 88+
- [ ] Safari 14+
- [ ] Edge 90+

#### React Native 版本
- [ ] iOS 13.0+
- [ ] Android API 21+ (5.0+)
- [ ] 不同屏幕尺寸
- [ ] 橫豎屏切換

## 🔧 環境設置

### 必要工具
```bash
# Node.js 18+
node --version

# npm 或 yarn
npm --version

# Expo CLI
npm install -g @expo/cli

# EAS CLI (可選)
npm install -g eas-cli
```

### 模擬器設置

#### iOS (macOS)
1. 安裝 Xcode
2. 打開 Xcode → Preferences → Components
3. 下載所需的模擬器版本

#### Android
1. 安裝 Android Studio
2. 打開 AVD Manager
3. 創建和啟動虛擬設備

## 🚨 常見問題解決

### Web 版本問題
```bash
# 頁面無法打開
# 1. 檢查文件路徑
# 2. 確保瀏覽器支持本地文件
# 3. 嘗試使用 http-server
npx http-server counter-app
```

### React Native 問題
```bash
# 清除緩存
npm run clean

# 重新安裝依賴
rm -rf node_modules package-lock.json
npm install

# 重置 Metro
npm start -- --reset-cache
```

### 模擬器問題
```bash
# iOS 模擬器重置
xcrun simctl erase all

# Android 模擬器重啟
adb kill-server
adb start-server
```

## 📞 技術支援

如果遇到問題：

1. **查看錯誤日誌** - 檢查控制台輸出
2. **搜索文檔** - 查看相關技術文檔
3. **社群求助** - 在開發者社群提問
4. **提交 Issue** - 在項目倉庫報告問題

---

**開始測試吧！** 🚀

建議測試順序：
1. 先體驗 Web 版本了解功能
2. 設置 React Native 環境
3. 運行手機/模擬器版本
4. 執行自動化測試驗證質量

祝您測試順利！ 🎉
