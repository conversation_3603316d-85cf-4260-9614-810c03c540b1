# 咒語計數器 React Native 版本

基於 React Native + Expo 開發的現代化修行助手應用，完全按照應用商店上架標準構建。

## 📱 應用概述

咒語計數器是一個專為佛教修行者設計的現代化數位工具，提供完整的修行管理功能。應用採用 React Native + Expo 技術棧，支持 iOS 和 Android 雙平台，具備商業級的用戶體驗和性能表現。

## 🎯 核心功能

### 1. 修行項目管理
- ✅ 創建和管理修行項目
- ✅ 設定目標數量和描述
- ✅ 實時進度追蹤和統計
- ✅ 項目完成度視覺化

### 2. 極簡計數界面
- ✅ 大按鈕觸摸友好設計
- ✅ 進度環實時顯示
- ✅ 快速操作 (+10, -1, 重置)
- ✅ 音效和觸覺反饋
- ✅ 會期時間和速度統計

### 3. 願望清單管理
- ✅ 添加和分類個人願望
- ✅ 願望狀態追蹤
- ✅ 功德灌注功能
- ✅ 祝福次數統計

### 4. 功德迴向系統
- ✅ 自定義迴向文編輯
- ✅ 預設迴向文模板
- ✅ 願望選擇和功德灌注
- ✅ 社群分享功能

### 5. 數據統計分析
- ✅ 今日修行統計
- ✅ 項目進度分析
- ✅ 歷史記錄查看
- ✅ 成就系統 (規劃中)

## 🛠️ 技術架構

### 前端技術棧
- **React Native 0.72.6** - 跨平台移動應用框架
- **Expo SDK 49** - 開發工具鏈和服務
- **TypeScript** - 類型安全的 JavaScript
- **React Navigation 6** - 導航管理
- **Expo Linear Gradient** - 漸變效果
- **React Native SVG** - SVG 圖形支持
- **Expo Audio** - 音效播放
- **Expo Haptics** - 觸覺反饋

### 狀態管理
- **React Hooks** - 本地狀態管理
- **AsyncStorage** - 數據持久化
- **Context API** - 全局狀態 (規劃中)

### 設計系統
- **Fitnest UI 風格** - 現代化漸變設計
- **響應式布局** - 適配各種屏幕尺寸
- **無障礙支持** - 符合 WCAG 標準
- **深色模式** - 規劃中

## 📁 項目結構

```
react-native/
├── App.tsx                 # 應用入口
├── app.json               # Expo 配置
├── eas.json               # EAS Build 配置
├── package.json           # 依賴管理
├── tsconfig.json          # TypeScript 配置
├── src/
│   ├── components/        # 可重用組件
│   │   ├── ProjectCard.tsx
│   │   ├── StatsCard.tsx
│   │   ├── RecentSessionItem.tsx
│   │   ├── CreateProjectModal.tsx
│   │   └── CreateWishModal.tsx
│   ├── screens/           # 頁面組件
│   │   ├── WelcomeScreen.tsx
│   │   ├── HomeScreen.tsx
│   │   ├── CounterScreen.tsx
│   │   ├── WishListScreen.tsx
│   │   └── MeritScreen.tsx
│   ├── services/          # 業務邏輯服務
│   │   ├── StorageService.ts
│   │   └── AudioService.ts
│   ├── types/             # TypeScript 類型定義
│   │   ├── index.ts
│   │   └── navigation.ts
│   ├── constants/         # 常量定義
│   │   └── Colors.ts
│   └── utils/             # 工具函數
├── assets/                # 靜態資源
│   ├── images/
│   ├── sounds/
│   └── fonts/
└── store-assets/          # 應用商店資源
    ├── icons/
    ├── screenshots/
    ├── descriptions/
    └── README.md
```

## 🚀 開發環境設置

### 前置要求
- Node.js 18+ 
- npm 或 yarn
- Expo CLI
- iOS Simulator (macOS) 或 Android Emulator
- Xcode (iOS 開發)
- Android Studio (Android 開發)

### 安裝步驟

1. **克隆項目**
```bash
git clone <repository-url>
cd counter-app/react-native
```

2. **安裝依賴**
```bash
npm install
# 或
yarn install
```

3. **啟動開發服務器**
```bash
npm start
# 或
expo start
```

4. **在模擬器中運行**
```bash
# iOS
npm run ios

# Android
npm run android

# Web (開發測試)
npm run web
```

## 📱 構建和部署

### 開發構建
```bash
# 開發版本 (包含調試工具)
eas build --profile development --platform all
```

### 預覽構建
```bash
# 預覽版本 (內部測試)
eas build --profile preview --platform all
```

### 生產構建
```bash
# iOS App Store
eas build --profile production --platform ios

# Google Play Store
eas build --profile production --platform android
```

### 提交到應用商店
```bash
# 提交到 App Store
eas submit --platform ios

# 提交到 Google Play
eas submit --platform android
```

## 🎨 設計規範

### 色彩系統
- **主色調**: #92A3FD (藍紫色)
- **次要色**: #9DCEFF (淺藍色)
- **強調色**: #C58BF2 (紫色)
- **成功色**: #42D742 (綠色)
- **警告色**: #FFD600 (黃色)
- **錯誤色**: #FF6B6B (紅色)

### 字體系統
- **主字體**: Poppins
- **備用字體**: 系統默認字體
- **字重**: 300, 400, 500, 600, 700

### 間距系統
- **基準**: 4px
- **常用間距**: 8px, 16px, 24px, 32px, 48px

### 圓角系統
- **小圓角**: 8px
- **中圓角**: 12px
- **大圓角**: 16px
- **超大圓角**: 20px

## 🧪 測試策略

### 單元測試
```bash
npm test
```

### 集成測試
- 使用 Detox 進行 E2E 測試
- 測試關鍵用戶流程

### 性能測試
- 使用 Flipper 進行性能分析
- 內存洩漏檢測
- 啟動時間優化

### 設備測試
- iOS: iPhone 12 Mini 到 iPhone 14 Pro Max
- Android: 各種屏幕尺寸和 Android 版本
- 平板設備支持

## 📊 性能優化

### 代碼優化
- 使用 React.memo 避免不必要的重渲染
- 實現虛擬列表處理大數據集
- 圖片懶加載和壓縮
- 代碼分割和動態導入

### 包大小優化
- 移除未使用的依賴
- 使用 Metro bundler 進行樹搖
- 圖片和音頻資源優化

### 運行時優化
- 使用 Hermes JavaScript 引擎
- 啟用新架構 (Fabric + TurboModules)
- 內存管理優化

## 🔒 安全考慮

### 數據安全
- 本地數據加密存儲
- 敏感信息不記錄日誌
- 安全的數據備份機制

### 隱私保護
- 最小化數據收集
- 透明的隱私政策
- 用戶數據控制權

### 代碼安全
- 依賴安全掃描
- 代碼混淆和保護
- API 安全通信

## 🌍 國際化支持

### 支持語言
- 繁體中文 (zh-TW) - 主要
- 簡體中文 (zh-CN) - 規劃中
- 英文 (en) - 規劃中

### 本地化內容
- 界面文字翻譯
- 日期時間格式
- 數字格式
- 文化適應性調整

## 📈 分析和監控

### 用戶分析
- 使用 Expo Analytics
- 用戶行為追蹤
- 功能使用統計

### 錯誤監控
- 使用 Sentry 錯誤追蹤
- 崩潰報告分析
- 性能監控

### A/B 測試
- 功能開關管理
- 用戶體驗優化
- 轉化率測試

## 🔄 持續集成/部署

### CI/CD 流程
- GitHub Actions 自動化
- 代碼質量檢查
- 自動化測試執行
- 自動構建和部署

### 版本管理
- 語義化版本控制
- 自動化版本號管理
- 變更日誌生成

## 📞 支援和維護

### 用戶支援
- **電子郵件**: <EMAIL>
- **網站**: https://mantracounter.app
- **文檔**: https://docs.mantracounter.app

### 開發團隊
- **主開發者**: [您的姓名]
- **設計師**: [設計師姓名]
- **測試工程師**: [測試工程師姓名]

### 維護計劃
- 定期安全更新
- 功能增強和優化
- 用戶反饋響應
- 平台適配更新

## 📄 許可證

本項目基於 MIT 許可證開源。詳見 [LICENSE](LICENSE) 文件。

## 🙏 致謝

- **設計靈感**: Fitnest UI Kit
- **圖標**: Ionicons
- **字體**: Google Fonts (Poppins)
- **開發框架**: React Native & Expo
- **社群支持**: React Native 和 Expo 開發者社群

---

**願此應用能幫助更多修行者精進修行，功德無量！** 🙏

## 📋 開發檢查清單

### 基礎功能
- [x] 項目創建和管理
- [x] 計數界面和邏輯
- [x] 願望清單功能
- [x] 功德迴向系統
- [x] 數據持久化
- [x] 音效和觸覺反饋

### 用戶體驗
- [x] 響應式設計
- [x] 流暢動畫效果
- [x] 直觀的導航
- [x] 錯誤處理
- [x] 加載狀態
- [x] 空狀態設計

### 技術要求
- [x] TypeScript 類型安全
- [x] 代碼結構清晰
- [x] 性能優化
- [x] 錯誤邊界處理
- [x] 內存管理
- [x] 網絡錯誤處理

### 商店準備
- [x] 應用圖標設計
- [x] 啟動畫面
- [x] 應用截圖
- [x] 商店描述
- [x] 隱私政策
- [x] 使用條款

### 測試覆蓋
- [ ] 單元測試
- [ ] 集成測試
- [ ] E2E 測試
- [ ] 性能測試
- [ ] 設備兼容性測試
- [ ] 用戶接受測試

### 部署準備
- [x] EAS Build 配置
- [x] 環境變量設置
- [x] 簽名證書
- [x] 商店帳號設置
- [ ] Beta 測試分發
- [ ] 正式發布流程
