#!/bin/bash

# 咒語計數器測試腳本
# 用於執行完整的測試套件

set -e

echo "🧪 開始執行咒語計數器完整測試套件..."

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數：打印帶顏色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 函數：檢查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message "❌ $1 未安裝，請先安裝" $RED
        exit 1
    fi
}

# 函數：運行測試並檢查結果
run_test() {
    local test_name=$1
    local test_command=$2
    
    print_message "🔄 運行 $test_name..." $BLUE
    
    if eval $test_command; then
        print_message "✅ $test_name 通過" $GREEN
        return 0
    else
        print_message "❌ $test_name 失敗" $RED
        return 1
    fi
}

# 檢查必要的工具
print_message "🔍 檢查測試環境..." $YELLOW
check_command "node"
check_command "npm"
check_command "npx"

# 檢查依賴是否安裝
if [ ! -d "node_modules" ]; then
    print_message "📦 安裝依賴..." $YELLOW
    npm install
fi

# 測試結果統計
total_tests=0
passed_tests=0
failed_tests=0

# 1. 代碼質量檢查
print_message "\n📋 第一階段：代碼質量檢查" $YELLOW
total_tests=$((total_tests + 3))

# TypeScript 類型檢查
if run_test "TypeScript 類型檢查" "npx tsc --noEmit"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# ESLint 代碼規範檢查
if run_test "ESLint 代碼規範檢查" "npx eslint src/ --ext .ts,.tsx"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# Prettier 代碼格式檢查
if run_test "Prettier 代碼格式檢查" "npx prettier --check src/"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# 2. 單元測試
print_message "\n🧪 第二階段：單元測試" $YELLOW
total_tests=$((total_tests + 1))

if run_test "Jest 單元測試" "npm test -- --coverage --watchAll=false"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# 3. 構建測試
print_message "\n🏗️ 第三階段：構建測試" $YELLOW
total_tests=$((total_tests + 2))

# Metro bundler 測試
if run_test "Metro Bundler 測試" "npx expo export --platform all --output-dir dist --clear"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# EAS 構建配置驗證
if run_test "EAS 構建配置驗證" "npx eas build --platform all --dry-run"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# 4. 性能測試
print_message "\n⚡ 第四階段：性能測試" $YELLOW
total_tests=$((total_tests + 2))

# Bundle 大小檢查
if run_test "Bundle 大小檢查" "npx expo export --platform all && du -sh dist/"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# 內存洩漏檢查
if run_test "內存洩漏檢查" "npm test -- --detectLeaks --watchAll=false"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# 5. E2E 測試（可選）
if command -v detox &> /dev/null; then
    print_message "\n🤖 第五階段：E2E 測試" $YELLOW
    total_tests=$((total_tests + 1))
    
    # 檢查模擬器是否可用
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS - 檢查 iOS 模擬器
        if xcrun simctl list devices | grep -q "Booted"; then
            if run_test "Detox E2E 測試 (iOS)" "npx detox test --configuration ios.sim.debug"; then
                passed_tests=$((passed_tests + 1))
            else
                failed_tests=$((failed_tests + 1))
            fi
        else
            print_message "⚠️ iOS 模擬器未運行，跳過 E2E 測試" $YELLOW
        fi
    else
        # Linux/Windows - 檢查 Android 模擬器
        if adb devices | grep -q "emulator"; then
            if run_test "Detox E2E 測試 (Android)" "npx detox test --configuration android.emu.debug"; then
                passed_tests=$((passed_tests + 1))
            else
                failed_tests=$((failed_tests + 1))
            fi
        else
            print_message "⚠️ Android 模擬器未運行，跳過 E2E 測試" $YELLOW
        fi
    fi
else
    print_message "⚠️ Detox 未安裝，跳過 E2E 測試" $YELLOW
fi

# 6. 安全檢查
print_message "\n🔒 第六階段：安全檢查" $YELLOW
total_tests=$((total_tests + 2))

# 依賴安全掃描
if run_test "依賴安全掃描" "npm audit --audit-level moderate"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# 敏感信息檢查
if run_test "敏感信息檢查" "grep -r -i 'password\|secret\|key\|token' src/ || true"; then
    passed_tests=$((passed_tests + 1))
else
    failed_tests=$((failed_tests + 1))
fi

# 7. 商店準備檢查
print_message "\n🏪 第七階段：商店準備檢查" $YELLOW
total_tests=$((total_tests + 4))

# 檢查必要的資源文件
if [ -f "assets/icon.png" ] && [ -f "assets/splash.png" ]; then
    print_message "✅ 應用圖標和啟動畫面檢查通過" $GREEN
    passed_tests=$((passed_tests + 1))
else
    print_message "❌ 缺少必要的應用圖標或啟動畫面" $RED
    failed_tests=$((failed_tests + 1))
fi

# 檢查應用配置
if grep -q "name" app.json && grep -q "version" app.json; then
    print_message "✅ 應用配置檢查通過" $GREEN
    passed_tests=$((passed_tests + 1))
else
    print_message "❌ 應用配置不完整" $RED
    failed_tests=$((failed_tests + 1))
fi

# 檢查隱私政策和使用條款
if [ -f "store-assets/privacy-policy.md" ] && [ -f "store-assets/terms-of-service.md" ]; then
    print_message "✅ 法律文件檢查通過" $GREEN
    passed_tests=$((passed_tests + 1))
else
    print_message "❌ 缺少隱私政策或使用條款" $RED
    failed_tests=$((failed_tests + 1))
fi

# 檢查商店描述
if [ -f "store-assets/descriptions/app-description-zh.md" ]; then
    print_message "✅ 商店描述檢查通過" $GREEN
    passed_tests=$((passed_tests + 1))
else
    print_message "❌ 缺少商店描述文件" $RED
    failed_tests=$((failed_tests + 1))
fi

# 測試結果總結
print_message "\n📊 測試結果總結" $YELLOW
print_message "總測試數: $total_tests" $BLUE
print_message "通過: $passed_tests" $GREEN
print_message "失敗: $failed_tests" $RED

# 計算成功率
success_rate=$((passed_tests * 100 / total_tests))
print_message "成功率: $success_rate%" $BLUE

# 生成測試報告
report_file="test-report-$(date +%Y%m%d-%H%M%S).txt"
{
    echo "咒語計數器測試報告"
    echo "===================="
    echo "測試時間: $(date)"
    echo "總測試數: $total_tests"
    echo "通過: $passed_tests"
    echo "失敗: $failed_tests"
    echo "成功率: $success_rate%"
    echo ""
    echo "詳細結果請查看上方輸出"
} > "$report_file"

print_message "\n📄 測試報告已保存到: $report_file" $BLUE

# 根據結果決定退出碼
if [ $failed_tests -eq 0 ]; then
    print_message "\n🎉 所有測試通過！應用已準備好發布。" $GREEN
    exit 0
else
    print_message "\n⚠️ 有 $failed_tests 個測試失敗，請修復後重新測試。" $RED
    exit 1
fi
