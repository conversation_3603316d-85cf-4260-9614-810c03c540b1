// 功德迴向頁面邏輯

class MeritPage {
    constructor() {
        this.completionData = null;
        this.selectedWishIds = [];
        this.templates = {
            universal: `願以此功德，莊嚴佛淨土。
上報四重恩，下濟三途苦。
若有見聞者，悉發菩提心。
盡此一報身，同生極樂國。`,
            family: `願以此功德，迴向我的家人。
願家人身體健康，平安喜樂。
願家庭和睦，福慧增長。
願所有眷屬，離苦得樂。`,
            health: `願以此功德，迴向身心健康。
願身體強健，遠離病苦。
願心靈清淨，智慧增長。
願眾生皆得，無病無憂。`
        };
        this.init();
    }

    init() {
        this.loadCompletionData();
        this.setupUI();
        this.loadWishes();
        this.setupEventListeners();
    }

    loadCompletionData() {
        const completionDataStr = sessionStorage.getItem('completionData');
        if (!completionDataStr) {
            MantraUtils.showToast('完成數據丟失，返回首頁');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
            return;
        }

        this.completionData = JSON.parse(completionDataStr);
    }

    setupUI() {
        if (!this.completionData) return;

        // 設置修行摘要
        const projectElement = document.getElementById('sessionProject');
        const countElement = document.getElementById('sessionCount');
        const durationElement = document.getElementById('sessionDuration');
        const progressElement = document.getElementById('totalProgress');

        if (projectElement) {
            projectElement.textContent = this.completionData.projectTitle;
        }

        if (countElement) {
            countElement.textContent = this.completionData.count;
        }

        if (durationElement) {
            durationElement.textContent = storage.formatDuration(this.completionData.duration);
        }

        if (progressElement) {
            const project = storage.getProject(this.completionData.projectId);
            if (project) {
                const stats = storage.getProjectStats(this.completionData.projectId);
                progressElement.textContent = `${stats.totalCount.toLocaleString()} / ${project.totalGoalCount.toLocaleString()}`;
            }
        }

        // 設置默認迴向文
        this.setDefaultDedication();
    }

    setDefaultDedication() {
        const textarea = document.getElementById('dedicationText');
        if (textarea && this.completionData) {
            const defaultText = `我剛剛完成了「${this.completionData.projectTitle}」的修行，本次持誦 ${this.completionData.count} 次，總計已達 ${this.getTotalCount()} 次。

願以此功德，迴向法界眾生，願眾生離苦得樂，早證菩提。`;

            textarea.value = defaultText;
        }
    }

    getTotalCount() {
        const project = storage.getProject(this.completionData.projectId);
        if (project) {
            const stats = storage.getProjectStats(this.completionData.projectId);
            return stats.totalCount.toLocaleString();
        }
        return '未知';
    }

    loadWishes() {
        const wishes = storage.getWishes();
        const wishesSelection = document.getElementById('wishesSelection');
        const emptyWishes = document.getElementById('emptyWishes');

        if (!wishesSelection) return;

        if (wishes.length === 0) {
            wishesSelection.style.display = 'none';
            if (emptyWishes) emptyWishes.style.display = 'block';
            return;
        }

        if (emptyWishes) emptyWishes.style.display = 'none';
        wishesSelection.style.display = 'flex';
        wishesSelection.innerHTML = '';

        wishes.forEach(wish => {
            const wishItem = this.createWishSelectionItem(wish);
            wishesSelection.appendChild(wishItem);
        });

        this.updateInfuseButton();
    }

    createWishSelectionItem(wish) {
        const item = document.createElement('div');
        item.className = 'wish-selection-item';
        item.dataset.wishId = wish.id;

        const categoryTag = wish.category 
            ? `<span class="wish-category-tag">${this.getCategoryName(wish.category)}</span>`
            : '';

        item.innerHTML = `
            <div class="wish-selection-text">${wish.text}</div>
            <div class="wish-selection-meta">
                ${categoryTag}
                <span class="wish-date">${storage.formatDate(wish.createdAt)}</span>
            </div>
        `;

        item.addEventListener('click', () => {
            this.toggleWishSelection(wish.id);
        });

        return item;
    }

    getCategoryName(category) {
        const categories = {
            health: '健康',
            family: '家庭',
            career: '事業',
            study: '學業',
            peace: '平安',
            other: '其他'
        };
        return categories[category] || category;
    }

    toggleWishSelection(wishId) {
        const item = document.querySelector(`[data-wish-id="${wishId}"]`);
        if (!item) return;

        const index = this.selectedWishIds.indexOf(wishId);
        if (index > -1) {
            // 取消選擇
            this.selectedWishIds.splice(index, 1);
            item.classList.remove('selected');
        } else {
            // 選擇
            this.selectedWishIds.push(wishId);
            item.classList.add('selected');
        }

        this.updateInfuseButton();
    }

    updateInfuseButton() {
        const infuseBtn = document.getElementById('infuseBtn');
        const selectedCount = document.getElementById('selectedCount');

        if (infuseBtn) {
            infuseBtn.disabled = this.selectedWishIds.length === 0;
        }

        if (selectedCount) {
            selectedCount.textContent = this.selectedWishIds.length;
            selectedCount.style.display = this.selectedWishIds.length > 0 ? 'flex' : 'none';
        }
    }

    useTemplate(templateType) {
        const textarea = document.getElementById('dedicationText');
        if (textarea && this.templates[templateType]) {
            const currentText = textarea.value;
            const templateText = this.templates[templateType];
            
            // 如果當前有文字，詢問是否替換
            if (currentText.trim() && !confirm('確定要替換當前的迴向文嗎？')) {
                return;
            }

            textarea.value = templateText;
        }
    }

    infuseWishes() {
        if (this.selectedWishIds.length === 0) {
            MantraUtils.showToast('請先選擇要灌注的願望');
            return;
        }

        const dedicationText = document.getElementById('dedicationText').value.trim();

        // 創建祝福記錄
        const blessing = {
            projectId: this.completionData.projectId,
            count: this.completionData.count,
            wishIds: [...this.selectedWishIds],
            dedicationText: dedicationText,
            sessionData: this.completionData
        };

        if (storage.addBlessing(blessing)) {
            this.showInfusionSuccess();
        } else {
            MantraUtils.showToast('灌注失敗，請重試');
        }
    }

    showInfusionSuccess() {
        const modal = document.getElementById('infusionSuccessModal');
        const resultElement = document.getElementById('infusionResult');

        if (modal && resultElement) {
            let resultHtml = `<h4>成功灌注到 ${this.selectedWishIds.length} 個願望：</h4>`;
            
            this.selectedWishIds.forEach(wishId => {
                const wish = storage.getWish(wishId);
                if (wish) {
                    resultHtml += `<div class="infused-wish">${wish.text}</div>`;
                }
            });

            resultElement.innerHTML = resultHtml;
            modal.style.display = 'flex';

            // 播放成功音效
            MantraUtils.playSound('success');
            MantraUtils.vibrate([200, 100, 200]);
        }
    }

    hideInfusionSuccess() {
        const modal = document.getElementById('infusionSuccessModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    shareToSocial() {
        const dedicationText = document.getElementById('dedicationText').value.trim();
        
        if (!dedicationText) {
            MantraUtils.showToast('請先編寫迴向文');
            return;
        }

        this.showSharePreview(dedicationText);
    }

    showSharePreview(text) {
        const modal = document.getElementById('sharePreviewModal');
        const previewElement = document.getElementById('sharePreview');

        if (modal && previewElement) {
            previewElement.textContent = text;
            modal.style.display = 'flex';
        }
    }

    hideSharePreview() {
        const modal = document.getElementById('sharePreviewModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    confirmShare() {
        const shareText = document.getElementById('sharePreview').textContent;
        
        if (navigator.share) {
            // 使用 Web Share API
            navigator.share({
                title: '修行功德迴向',
                text: shareText
            }).then(() => {
                MantraUtils.showToast('分享成功！');
                this.hideSharePreview();
            }).catch((error) => {
                console.log('分享失敗:', error);
                this.fallbackShare(shareText);
            });
        } else {
            this.fallbackShare(shareText);
        }
    }

    fallbackShare(text) {
        // 備用分享方法：複製到剪貼板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                MantraUtils.showToast('內容已複製到剪貼板');
                this.hideSharePreview();
            }).catch(() => {
                this.manualCopy(text);
            });
        } else {
            this.manualCopy(text);
        }
    }

    manualCopy(text) {
        // 手動複製方法
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        
        try {
            document.execCommand('copy');
            MantraUtils.showToast('內容已複製到剪貼板');
        } catch (err) {
            MantraUtils.showToast('複製失敗，請手動複製');
        }
        
        document.body.removeChild(textArea);
        this.hideSharePreview();
    }

    completeSession() {
        // 清除會期數據
        sessionStorage.removeItem('currentSession');
        sessionStorage.removeItem('completionData');
        
        // 返回首頁
        window.location.href = 'index.html';
    }

    goToWishList() {
        window.location.href = 'wishlist.html';
    }

    goBack() {
        if (confirm('確定要返回嗎？當前的迴向內容將會丟失。')) {
            window.location.href = 'index.html';
        }
    }

    setupEventListeners() {
        // 迴向文字數統計
        const textarea = document.getElementById('dedicationText');
        if (textarea) {
            textarea.addEventListener('input', () => {
                // 可以添加字數統計功能
            });
        }
    }
}

// 全局函數
function useTemplate(templateType) {
    if (window.meritPage) {
        window.meritPage.useTemplate(templateType);
    }
}

function infuseWishes() {
    if (window.meritPage) {
        window.meritPage.infuseWishes();
    }
}

function hideInfusionSuccess() {
    if (window.meritPage) {
        window.meritPage.hideInfusionSuccess();
    }
}

function shareToSocial() {
    if (window.meritPage) {
        window.meritPage.shareToSocial();
    }
}

function hideSharePreview() {
    if (window.meritPage) {
        window.meritPage.hideSharePreview();
    }
}

function confirmShare() {
    if (window.meritPage) {
        window.meritPage.confirmShare();
    }
}

function completeSession() {
    if (window.meritPage) {
        window.meritPage.completeSession();
    }
}

function goToWishList() {
    if (window.meritPage) {
        window.meritPage.goToWishList();
    }
}

function goBack() {
    if (window.meritPage) {
        window.meritPage.goBack();
    }
}

// 初始化迴向頁面
document.addEventListener('DOMContentLoaded', () => {
    window.meritPage = new MeritPage();
});
