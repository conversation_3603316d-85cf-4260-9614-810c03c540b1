<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功德迴向 - 咒語計數器</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/merit.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 頂部導航 -->
        <header class="header">
            <div class="header-content">
                <button class="back-btn" onclick="goBack()">
                    <svg viewBox="0 0 24 24">
                        <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                    </svg>
                </button>
                <h1 class="page-title">功德迴向</h1>
                <div class="header-placeholder"></div>
            </div>
        </header>

        <!-- 主要內容 -->
        <main class="main-content">
            <!-- 完成慶祝 -->
            <section class="completion-celebration">
                <div class="celebration-icon">
                    <svg viewBox="0 0 24 24">
                        <path d="M12,2L13.09,8.26L22,9L14.74,13.74L17.18,22L12,17.27L6.82,22L9.26,13.74L2,9L10.91,8.26L12,2Z"/>
                    </svg>
                </div>
                <h2>🎉 修行圓滿</h2>
                <p>恭喜您完成了本次修行會期</p>
            </section>

            <!-- 修行摘要 -->
            <section class="session-summary">
                <h3>本次修行摘要</h3>
                <div class="summary-card">
                    <div class="summary-item">
                        <span class="summary-label">修行項目</span>
                        <span class="summary-value" id="sessionProject">六字大明咒</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">持誦次數</span>
                        <span class="summary-value highlight" id="sessionCount">108</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">用時</span>
                        <span class="summary-value" id="sessionDuration">5分30秒</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">總進度</span>
                        <span class="summary-value" id="totalProgress">628 / 1,000,000</span>
                    </div>
                </div>
            </section>

            <!-- 迴向文編輯 -->
            <section class="merit-dedication">
                <h3>功德迴向</h3>
                <div class="dedication-form">
                    <div class="form-group">
                        <label for="dedicationText">迴向文</label>
                        <textarea id="dedicationText" placeholder="編輯您的迴向文..." rows="6"></textarea>
                    </div>
                    
                    <!-- 預設迴向文模板 -->
                    <div class="template-section">
                        <h4>常用迴向文</h4>
                        <div class="template-buttons">
                            <button class="template-btn" onclick="useTemplate('universal')">
                                <span class="template-title">普迴向</span>
                                <span class="template-preview">願以此功德，莊嚴佛淨土...</span>
                            </button>
                            <button class="template-btn" onclick="useTemplate('family')">
                                <span class="template-title">家庭迴向</span>
                                <span class="template-preview">願以此功德，迴向家人平安...</span>
                            </button>
                            <button class="template-btn" onclick="useTemplate('health')">
                                <span class="template-title">健康迴向</span>
                                <span class="template-preview">願以此功德，迴向身心健康...</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 願望灌注 -->
            <section class="wish-infusion">
                <h3>灌注願望</h3>
                <p class="section-description">選擇要灌注功德的願望，讓修行的力量加持您的心願</p>
                
                <div class="wishes-selection" id="wishesSelection">
                    <!-- 願望選擇項目將通過 JavaScript 動態生成 -->
                </div>

                <!-- 空狀態 -->
                <div class="empty-wishes" id="emptyWishes" style="display: none;">
                    <div class="empty-icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.04L12,21.35Z"/>
                        </svg>
                    </div>
                    <p>還沒有願望可以灌注</p>
                    <button class="secondary-btn" onclick="goToWishList()">前往願望清單</button>
                </div>
            </section>

            <!-- 操作按鈕 -->
            <section class="action-buttons">
                <div class="button-grid">
                    <button class="action-btn infuse-btn" onclick="infuseWishes()" id="infuseBtn" disabled>
                        <div class="btn-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.04L12,21.35Z"/>
                            </svg>
                        </div>
                        <span>灌注到願望清單</span>
                        <span class="selected-count" id="selectedCount">0</span>
                    </button>
                    
                    <button class="action-btn share-btn" onclick="shareToSocial()">
                        <div class="btn-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.6 20.92,19A2.92,2.92 0 0,0 18,16.08Z"/>
                            </svg>
                        </div>
                        <span>分享到社群</span>
                    </button>
                </div>
                
                <button class="complete-btn" onclick="completeSession()">
                    <span>完成並返回</span>
                    <svg viewBox="0 0 24 24">
                        <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                    </svg>
                </button>
            </section>
        </main>
    </div>

    <!-- 灌注成功模態框 -->
    <div class="modal-overlay" id="infusionSuccessModal" style="display: none;">
        <div class="modal-content success-modal">
            <div class="success-icon">
                <svg viewBox="0 0 24 24">
                    <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                </svg>
            </div>
            <h3>功德灌注成功</h3>
            <div class="infusion-result" id="infusionResult">
                <!-- 灌注結果將通過 JavaScript 動態生成 -->
            </div>
            <div class="modal-buttons">
                <button class="modal-btn primary" onclick="hideInfusionSuccess()">確定</button>
            </div>
        </div>
    </div>

    <!-- 分享預覽模態框 -->
    <div class="modal-overlay" id="sharePreviewModal" style="display: none;">
        <div class="modal-content">
            <h3>分享預覽</h3>
            <div class="share-preview" id="sharePreview">
                <!-- 分享內容預覽 -->
            </div>
            <div class="modal-buttons">
                <button class="modal-btn secondary" onclick="hideSharePreview()">取消</button>
                <button class="modal-btn primary" onclick="confirmShare()">確認分享</button>
            </div>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script src="js/merit.js"></script>
</body>
</html>
