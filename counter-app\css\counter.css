/* 計數頁面專用樣式 */

.counter-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    display: flex;
    flex-direction: column;
    max-width: 375px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

/* 頂部信息區 */
.counter-header {
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.back-btn,
.pause-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-50);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover,
.pause-btn:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.back-btn svg,
.pause-btn svg {
    width: 20px;
    height: 20px;
    fill: var(--gray-400);
}

.header-info {
    text-align: center;
    flex: 1;
    margin: 0 var(--spacing-md);
}

.project-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-xs);
}

.progress-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.total-progress {
    font-size: 14px;
    color: var(--gray-400);
}

.progress-percentage {
    font-size: 12px;
    color: var(--primary-color);
    font-weight: 600;
    background: var(--gradient-card);
    padding: 2px 8px;
    border-radius: 10px;
}

/* 主計數區域 */
.counter-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) var(--spacing-lg);
    gap: var(--spacing-2xl);
}

/* 目標進度環 */
.target-ring {
    position: relative;
    width: 200px;
    height: 200px;
}

.ring-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.ring-bg {
    fill: none;
    stroke: var(--gray-100);
    stroke-width: 12;
}

.ring-progress {
    fill: none;
    stroke: url(#ringGradient);
    stroke-width: 12;
    stroke-linecap: round;
    stroke-dasharray: 565.48;
    stroke-dashoffset: 565.48;
    transition: stroke-dashoffset 0.5s ease;
}

.ring-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.session-count {
    font-size: 48px;
    font-weight: 700;
    color: var(--gray-500);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.session-target {
    font-size: 16px;
    color: var(--gray-400);
    font-weight: 500;
}

.session-target span {
    color: var(--primary-color);
    font-weight: 600;
}

/* 計數按鈕 */
.counter-button-area {
    width: 100%;
    display: flex;
    justify-content: center;
}

.counter-btn {
    width: 200px;
    height: 200px;
    border: none;
    border-radius: 50%;
    background: var(--gradient-primary);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-lg);
}

.counter-btn:hover {
    transform: scale(1.02);
    box-shadow: 0 12px 40px rgba(146, 163, 253, 0.4);
}

.counter-btn:active {
    transform: scale(0.98);
}

.btn-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--white);
}

.btn-icon {
    width: 48px;
    height: 48px;
    margin-bottom: var(--spacing-sm);
}

.btn-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.btn-text {
    font-size: 16px;
    font-weight: 600;
}

/* 按鈕波紋效果 */
.btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.btn-ripple.animate {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    to {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

/* 快速操作 */
.quick-actions {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.quick-action-btn {
    width: 60px;
    height: 60px;
    border: 2px solid var(--gray-200);
    background: var(--white);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-500);
}

.quick-action-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    background: var(--gradient-card);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.quick-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quick-action-btn svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* 底部狀態 */
.counter-footer {
    padding: var(--spacing-lg);
    background: var(--white);
    border-top: 1px solid var(--gray-100);
}

.session-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.info-item {
    text-align: center;
}

.info-label {
    display: block;
    font-size: 10px;
    color: var(--gray-400);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.info-value {
    display: block;
    font-size: 14px;
    color: var(--gray-500);
    font-weight: 600;
}

/* 慶祝模態框 */
.celebration-modal {
    background: rgba(0, 0, 0, 0.8);
}

.celebration-content {
    text-align: center;
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    position: relative;
    overflow: hidden;
}

.celebration-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.celebration-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-success);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: celebrationPulse 2s infinite;
}

.celebration-icon svg {
    width: 40px;
    height: 40px;
    fill: var(--white);
}

@keyframes celebrationPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.celebration-content h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-500);
    margin-bottom: var(--spacing-lg);
}

.completion-stats {
    background: var(--gray-50);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.completion-stats p {
    font-size: 14px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-md);
}

.stats-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 14px;
    color: var(--gray-500);
}

.stats-row:last-child {
    margin-bottom: 0;
}

.stats-row strong {
    color: var(--primary-color);
    font-weight: 600;
}

/* 暫停統計 */
.pause-stats {
    background: var(--gray-50);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin: var(--spacing-lg) 0;
    text-align: center;
}

.pause-stats p {
    font-size: 14px;
    color: var(--gray-500);
    margin-bottom: var(--spacing-sm);
}

.pause-stats p:last-child {
    margin-bottom: 0;
}

.pause-stats span {
    color: var(--primary-color);
    font-weight: 600;
}

/* 響應式設計 */
@media (max-width: 320px) {
    .counter-main {
        padding: var(--spacing-lg) var(--spacing-md);
        gap: var(--spacing-xl);
    }
    
    .target-ring {
        width: 160px;
        height: 160px;
    }
    
    .counter-btn {
        width: 160px;
        height: 160px;
    }
    
    .session-count {
        font-size: 36px;
    }
    
    .quick-action-btn {
        width: 50px;
        height: 50px;
        font-size: 12px;
    }
}

@media (min-width: 768px) {
    .counter-container {
        max-width: 768px;
    }
    
    .target-ring {
        width: 240px;
        height: 240px;
    }
    
    .counter-btn {
        width: 240px;
        height: 240px;
    }
    
    .session-count {
        font-size: 56px;
    }
}
