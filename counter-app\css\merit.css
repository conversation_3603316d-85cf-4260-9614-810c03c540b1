/* 功德迴向頁面專用樣式 */

.header-placeholder {
    width: 40px;
}

/* 完成慶祝 */
.completion-celebration {
    text-align: center;
    padding: var(--spacing-xl) var(--spacing-lg);
    background: var(--gradient-card);
    border-radius: var(--radius-xl);
    margin-bottom: var(--spacing-xl);
}

.celebration-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-success);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: celebrationPulse 2s infinite;
}

.celebration-icon svg {
    width: 40px;
    height: 40px;
    fill: var(--white);
}

@keyframes celebrationPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.completion-celebration h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-500);
    margin-bottom: var(--spacing-sm);
}

.completion-celebration p {
    font-size: 14px;
    color: var(--gray-400);
}

/* 修行摘要 */
.session-summary {
    margin-bottom: var(--spacing-xl);
}

.session-summary h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-md);
}

.summary-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--gray-100);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    font-size: 14px;
    color: var(--gray-400);
    font-weight: 500;
}

.summary-value {
    font-size: 14px;
    color: var(--gray-500);
    font-weight: 600;
}

.summary-value.highlight {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 700;
}

/* 迴向文編輯 */
.merit-dedication {
    margin-bottom: var(--spacing-xl);
}

.merit-dedication h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-md);
}

.dedication-form {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.dedication-form textarea {
    min-height: 120px;
    resize: vertical;
}

/* 模板區域 */
.template-section {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-100);
}

.template-section h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-md);
}

.template-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.template-btn {
    background: var(--gray-50);
    border: 2px solid var(--gray-100);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
}

.template-btn:hover {
    border-color: var(--primary-color);
    background: var(--gradient-card);
}

.template-title {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-xs);
}

.template-preview {
    display: block;
    font-size: 12px;
    color: var(--gray-400);
    line-height: 1.4;
}

/* 願望灌注 */
.wish-infusion {
    margin-bottom: var(--spacing-xl);
}

.wish-infusion h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-sm);
}

.section-description {
    font-size: 14px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-lg);
}

.wishes-selection {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.wish-selection-item {
    background: var(--white);
    border: 2px solid var(--gray-100);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.wish-selection-item:hover {
    border-color: var(--primary-color);
    background: var(--gradient-card);
}

.wish-selection-item.selected {
    border-color: var(--primary-color);
    background: var(--gradient-card);
}

.wish-selection-item.selected::after {
    content: '';
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: var(--radius-full);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='white' d='M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z'/%3E%3C/svg%3E");
    background-size: 12px;
    background-repeat: no-repeat;
    background-position: center;
}

.wish-selection-text {
    font-size: 14px;
    color: var(--gray-500);
    line-height: 1.4;
    margin-bottom: var(--spacing-sm);
}

.wish-selection-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--gray-400);
}

.wish-category-tag {
    background: var(--gradient-card);
    color: var(--primary-color);
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

/* 空狀態 */
.empty-wishes {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.empty-wishes .empty-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: var(--gradient-card);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-wishes .empty-icon svg {
    width: 30px;
    height: 30px;
    fill: var(--primary-color);
}

.empty-wishes p {
    font-size: 14px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-md);
}

.secondary-btn {
    background: var(--gray-100);
    color: var(--gray-500);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-btn:hover {
    background: var(--gray-200);
}

/* 操作按鈕 */
.action-buttons {
    margin-bottom: var(--spacing-xl);
}

.button-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.action-btn {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
}

.action-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    background: var(--gradient-card);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn .btn-icon {
    width: 32px;
    height: 32px;
    background: var(--gradient-primary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn .btn-icon svg {
    width: 18px;
    height: 18px;
    fill: var(--white);
}

.action-btn span {
    font-size: 12px;
    font-weight: 500;
    color: var(--gray-500);
    text-align: center;
}

.selected-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--error-color);
    color: var(--white);
    border-radius: var(--radius-full);
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
}

.complete-btn {
    width: 100%;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.complete-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.complete-btn svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* 成功模態框 */
.success-modal {
    text-align: center;
}

.success-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-success);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

.success-icon svg {
    width: 30px;
    height: 30px;
    fill: var(--white);
}

.infusion-result {
    background: var(--gray-50);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    text-align: left;
}

.infusion-result h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-sm);
}

.infused-wish {
    background: var(--white);
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: 12px;
    color: var(--gray-500);
}

.infused-wish:last-child {
    margin-bottom: 0;
}

/* 分享預覽 */
.share-preview {
    background: var(--gray-50);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    font-size: 14px;
    color: var(--gray-500);
    line-height: 1.6;
    white-space: pre-line;
}

/* 響應式設計 */
@media (max-width: 320px) {
    .button-grid {
        grid-template-columns: 1fr;
    }
    
    .template-buttons {
        gap: var(--spacing-xs);
    }
    
    .action-btn {
        padding: var(--spacing-md);
    }
}

@media (min-width: 768px) {
    .wishes-selection {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }
    
    .button-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
