{"name": "mantra-counter", "version": "1.0.0", "description": "咒語計數器 - 現代化修行助手", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:preview": "eas build --profile preview --platform all", "build:production": "eas build --profile production --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --watchAll=false", "test:integration": "jest --testPathPattern=integration", "test:e2e": "detox test", "test:e2e:build": "detox build", "test:performance": "jest --testPathPattern=performance", "test:all": "./scripts/test.sh", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/", "format:check": "prettier --check src/", "type-check": "tsc --noEmit", "clean": "expo r -c", "clean:cache": "expo r -c && npm start -- --reset-cache", "analyze": "npx expo export --platform all && npx bundlesize", "prepare": "husky install"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "expo": "~49.0.15", "expo-av": "~13.4.1", "expo-constants": "~14.4.2", "expo-haptics": "~12.4.0", "expo-linear-gradient": "~12.3.0", "expo-sharing": "~11.5.0", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.3.0", "@types/jest": "^29.5.5", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "detox": "^20.13.0", "eslint": "^8.44.0", "eslint-config-expo": "^7.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "jest": "^29.2.1", "jest-expo": "^49.0.0", "prettier": "^3.0.3", "react-test-renderer": "18.2.0", "typescript": "^5.1.3"}, "keywords": ["mantra", "counter", "meditation", "buddhism", "prayer", "spiritual", "react-native", "expo"], "author": {"name": "Mantra Counter Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mantracounter/app.git"}, "bugs": {"url": "https://github.com/mantracounter/app/issues"}, "homepage": "https://mantracounter.app", "private": false}