import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';

import { Colors } from '../constants/Colors';
import { StorageService } from '../services/StorageService';
import { AudioService } from '../services/AudioService';
import { Project, TodayStats, Session } from '../types';
import { RootStackScreenProps } from '../types/navigation';

// Components
import ProjectCard from '../components/ProjectCard';
import StatsCard from '../components/StatsCard';
import RecentSessionItem from '../components/RecentSessionItem';
import CreateProjectModal from '../components/CreateProjectModal';

type HomeScreenProps = RootStackScreenProps<'Main'>;

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [todayStats, setTodayStats] = useState<TodayStats>({
    sessionCount: 0,
    totalCount: 0,
    totalDuration: 0,
  });
  const [recentSessions, setRecentSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // 加載數據
  const loadData = async () => {
    try {
      const [projectsData, statsData, sessionsData] = await Promise.all([
        StorageService.getProjects(),
        StorageService.getTodayStats(),
        StorageService.getRecentSessions(3),
      ]);

      setProjects(projectsData);
      setTodayStats(statsData);
      setRecentSessions(sessionsData);
    } catch (error) {
      console.error('Failed to load home data:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // 頁面聚焦時重新加載數據
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [])
  );

  // 下拉刷新
  const onRefresh = () => {
    setIsRefreshing(true);
    loadData();
  };

  // 獲取問候語
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 6) return '夜深了，願您安眠';
    if (hour < 12) return '早安，開始今日修行';
    if (hour < 18) return '午安，繼續精進修行';
    return '晚安，圓滿今日修行';
  };

  // 處理項目點擊
  const handleProjectPress = async (project: Project) => {
    await AudioService.buttonPress();
    
    Alert.alert(
      '開始修行',
      `準備開始修行：${project.title}`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '開始',
          onPress: () => showSessionSetup(project),
        },
      ]
    );
  };

  // 顯示會期設定
  const showSessionSetup = (project: Project) => {
    Alert.prompt(
      '設定目標',
      '請輸入本次修行的目標次數：',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '開始修行',
          onPress: (target) => {
            const targetNumber = parseInt(target || '108');
            if (targetNumber > 0) {
              navigation.navigate('Counter', {
                projectId: project.id,
                projectTitle: project.title,
                target: targetNumber,
              });
            }
          },
        },
      ],
      'plain-text',
      '108'
    );
  };

  // 創建項目
  const handleCreateProject = async (projectData: {
    title: string;
    description: string;
    totalGoalCount: number;
  }) => {
    try {
      const newProject = await StorageService.addProject({
        ...projectData,
        currentTotalCount: 0,
      });

      if (newProject) {
        await AudioService.buttonSuccess();
        setProjects(prev => [...prev, newProject]);
        setShowCreateModal(false);
      } else {
        await AudioService.buttonError();
        Alert.alert('錯誤', '創建項目失敗，請重試');
      }
    } catch (error) {
      console.error('Failed to create project:', error);
      await AudioService.buttonError();
      Alert.alert('錯誤', '創建項目失敗，請重試');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* 頂部導航 */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>{getGreeting()}</Text>
            <Text style={styles.subtitle}>願所有功德迴向眾生</Text>
          </View>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowCreateModal(true)}
          >
            <LinearGradient
              colors={Colors.gradientPrimary}
              style={styles.addButtonGradient}
            >
              <Ionicons name="add" size={24} color={Colors.white} />
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* 今日統計 */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>今日修行</Text>
          <View style={styles.statsGrid}>
            <StatsCard
              icon="time-outline"
              title="修行次數"
              value={todayStats.sessionCount.toString()}
              color={Colors.primary}
            />
            <StatsCard
              icon="star-outline"
              title="持誦總數"
              value={todayStats.totalCount.toLocaleString()}
              color={Colors.accent}
            />
          </View>
        </View>

        {/* 修行項目 */}
        <View style={styles.projectsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>修行項目</Text>
            {projects.length > 3 && (
              <TouchableOpacity>
                <Text style={styles.seeMoreText}>查看全部</Text>
              </TouchableOpacity>
            )}
          </View>

          {projects.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons
                name="flower-outline"
                size={64}
                color={Colors.gray300}
                style={styles.emptyIcon}
              />
              <Text style={styles.emptyTitle}>開始您的修行之旅</Text>
              <Text style={styles.emptyDescription}>
                點擊右上角的 + 號創建第一個修行項目
              </Text>
              <TouchableOpacity
                style={styles.emptyButton}
                onPress={() => setShowCreateModal(true)}
              >
                <Text style={styles.emptyButtonText}>創建項目</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.projectsList}>
              {projects.slice(0, 3).map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onPress={() => handleProjectPress(project)}
                />
              ))}
            </View>
          )}
        </View>

        {/* 最近完成 */}
        {recentSessions.length > 0 && (
          <View style={styles.recentSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>最近完成</Text>
              <TouchableOpacity>
                <Text style={styles.seeMoreText}>查看歷史</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.recentList}>
              {recentSessions.map((session) => (
                <RecentSessionItem
                  key={session.id}
                  session={session}
                  onPress={() => {
                    // 導航到會期詳情
                  }}
                />
              ))}
            </View>
          </View>
        )}
      </ScrollView>

      {/* 創建項目模態框 */}
      <CreateProjectModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateProject}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textSecondary,
  },
  addButton: {
    width: 48,
    height: 48,
  },
  addButtonGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  projectsSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  seeMoreText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.primary,
  },
  projectsList: {
    gap: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 32,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  emptyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
  },
  recentSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  recentList: {
    gap: 12,
  },
});

export default HomeScreen;
