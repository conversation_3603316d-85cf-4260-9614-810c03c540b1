import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  Project, 
  Session, 
  Wish, 
  Blessing, 
  AppSettings, 
  StorageKeys,
  TodayStats,
  ProjectStats,
  AllStats,
  ExportData
} from '../types';

export class StorageService {
  // 初始化存儲服務
  static async initialize(): Promise<void> {
    try {
      // 檢查並初始化默認數據
      await this.initializeDefaultData();
    } catch (error) {
      console.error('Storage initialization failed:', error);
      throw error;
    }
  }

  // 初始化默認數據
  private static async initializeDefaultData(): Promise<void> {
    const projects = await this.getProjects();
    const wishes = await this.getWishes();
    const sessions = await this.getSessions();
    const blessings = await this.getBlessings();
    const settings = await this.getSettings();

    if (!projects) await this.saveProjects([]);
    if (!wishes) await this.saveWishes([]);
    if (!sessions) await this.saveSessions([]);
    if (!blessings) await this.saveBlessings([]);
    if (!settings) await this.saveSettings(this.getDefaultSettings());
  }

  // 獲取默認設置
  private static getDefaultSettings(): AppSettings {
    return {
      soundEnabled: true,
      vibrationEnabled: true,
      theme: 'light',
      language: 'zh-TW',
      autoSave: true,
      reminderEnabled: false,
    };
  }

  // 通用存儲方法
  private static async save<T>(key: string, data: T): Promise<boolean> {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error(`Failed to save ${key}:`, error);
      return false;
    }
  }

  // 通用讀取方法
  private static async load<T>(key: string): Promise<T | null> {
    try {
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`Failed to load ${key}:`, error);
      return null;
    }
  }

  // 修行項目管理
  static async getProjects(): Promise<Project[]> {
    return (await this.load<Project[]>(StorageKeys.PROJECTS)) || [];
  }

  static async saveProjects(projects: Project[]): Promise<boolean> {
    return this.save(StorageKeys.PROJECTS, projects);
  }

  static async addProject(project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project | null> {
    try {
      const projects = await this.getProjects();
      const newProject: Project = {
        ...project,
        id: this.generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      projects.push(newProject);
      const success = await this.saveProjects(projects);
      return success ? newProject : null;
    } catch (error) {
      console.error('Failed to add project:', error);
      return null;
    }
  }

  static async updateProject(projectId: string, updates: Partial<Project>): Promise<boolean> {
    try {
      const projects = await this.getProjects();
      const index = projects.findIndex(p => p.id === projectId);
      
      if (index === -1) return false;
      
      projects[index] = {
        ...projects[index],
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      
      return this.saveProjects(projects);
    } catch (error) {
      console.error('Failed to update project:', error);
      return false;
    }
  }

  static async deleteProject(projectId: string): Promise<boolean> {
    try {
      const projects = await this.getProjects();
      const filteredProjects = projects.filter(p => p.id !== projectId);
      return this.saveProjects(filteredProjects);
    } catch (error) {
      console.error('Failed to delete project:', error);
      return false;
    }
  }

  static async getProject(projectId: string): Promise<Project | null> {
    try {
      const projects = await this.getProjects();
      return projects.find(p => p.id === projectId) || null;
    } catch (error) {
      console.error('Failed to get project:', error);
      return null;
    }
  }

  // 修行會期管理
  static async getSessions(): Promise<Session[]> {
    return (await this.load<Session[]>(StorageKeys.SESSIONS)) || [];
  }

  static async saveSessions(sessions: Session[]): Promise<boolean> {
    return this.save(StorageKeys.SESSIONS, sessions);
  }

  static async addSession(session: Omit<Session, 'id' | 'createdAt'>): Promise<Session | null> {
    try {
      const sessions = await this.getSessions();
      const newSession: Session = {
        ...session,
        id: this.generateId(),
        createdAt: new Date().toISOString(),
      };
      
      sessions.push(newSession);
      const success = await this.saveSessions(sessions);
      return success ? newSession : null;
    } catch (error) {
      console.error('Failed to add session:', error);
      return null;
    }
  }

  static async getSessionsByProject(projectId: string): Promise<Session[]> {
    try {
      const sessions = await this.getSessions();
      return sessions.filter(s => s.projectId === projectId);
    } catch (error) {
      console.error('Failed to get sessions by project:', error);
      return [];
    }
  }

  static async getRecentSessions(limit: number = 5): Promise<Session[]> {
    try {
      const sessions = await this.getSessions();
      return sessions
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Failed to get recent sessions:', error);
      return [];
    }
  }

  // 願望管理
  static async getWishes(): Promise<Wish[]> {
    return (await this.load<Wish[]>(StorageKeys.WISHES)) || [];
  }

  static async saveWishes(wishes: Wish[]): Promise<boolean> {
    return this.save(StorageKeys.WISHES, wishes);
  }

  static async addWish(wish: Omit<Wish, 'id' | 'blessedCount' | 'createdAt'>): Promise<Wish | null> {
    try {
      const wishes = await this.getWishes();
      const newWish: Wish = {
        ...wish,
        id: this.generateId(),
        blessedCount: 0,
        createdAt: new Date().toISOString(),
      };
      
      wishes.push(newWish);
      const success = await this.saveWishes(wishes);
      return success ? newWish : null;
    } catch (error) {
      console.error('Failed to add wish:', error);
      return null;
    }
  }

  static async updateWish(wishId: string, updates: Partial<Wish>): Promise<boolean> {
    try {
      const wishes = await this.getWishes();
      const index = wishes.findIndex(w => w.id === wishId);
      
      if (index === -1) return false;
      
      wishes[index] = { ...wishes[index], ...updates };
      return this.saveWishes(wishes);
    } catch (error) {
      console.error('Failed to update wish:', error);
      return false;
    }
  }

  static async deleteWish(wishId: string): Promise<boolean> {
    try {
      const wishes = await this.getWishes();
      const filteredWishes = wishes.filter(w => w.id !== wishId);
      return this.saveWishes(filteredWishes);
    } catch (error) {
      console.error('Failed to delete wish:', error);
      return false;
    }
  }

  static async getWish(wishId: string): Promise<Wish | null> {
    try {
      const wishes = await this.getWishes();
      return wishes.find(w => w.id === wishId) || null;
    } catch (error) {
      console.error('Failed to get wish:', error);
      return null;
    }
  }

  // 祝福記錄管理
  static async getBlessings(): Promise<Blessing[]> {
    return (await this.load<Blessing[]>(StorageKeys.BLESSINGS)) || [];
  }

  static async saveBlessings(blessings: Blessing[]): Promise<boolean> {
    return this.save(StorageKeys.BLESSINGS, blessings);
  }

  static async addBlessing(blessing: Omit<Blessing, 'id' | 'createdAt'>): Promise<Blessing | null> {
    try {
      const blessings = await this.getBlessings();
      const newBlessing: Blessing = {
        ...blessing,
        id: this.generateId(),
        createdAt: new Date().toISOString(),
      };
      
      blessings.push(newBlessing);
      
      // 更新願望的祝福計數
      if (blessing.wishIds && blessing.wishIds.length > 0) {
        await this.incrementWishBlessedCounts(blessing.wishIds);
      }
      
      const success = await this.saveBlessings(blessings);
      return success ? newBlessing : null;
    } catch (error) {
      console.error('Failed to add blessing:', error);
      return null;
    }
  }

  private static async incrementWishBlessedCounts(wishIds: string[]): Promise<void> {
    try {
      const wishes = await this.getWishes();
      const now = new Date().toISOString();
      
      wishIds.forEach(wishId => {
        const index = wishes.findIndex(w => w.id === wishId);
        if (index !== -1) {
          wishes[index].blessedCount = (wishes[index].blessedCount || 0) + 1;
          wishes[index].lastBlessedAt = now;
        }
      });
      
      await this.saveWishes(wishes);
    } catch (error) {
      console.error('Failed to increment wish blessed counts:', error);
    }
  }

  // 設置管理
  static async getSettings(): Promise<AppSettings | null> {
    return this.load<AppSettings>(StorageKeys.SETTINGS);
  }

  static async saveSettings(settings: AppSettings): Promise<boolean> {
    return this.save(StorageKeys.SETTINGS, settings);
  }

  static async updateSettings(updates: Partial<AppSettings>): Promise<boolean> {
    try {
      const settings = await this.getSettings();
      if (!settings) return false;
      
      const newSettings = { ...settings, ...updates };
      return this.saveSettings(newSettings);
    } catch (error) {
      console.error('Failed to update settings:', error);
      return false;
    }
  }

  // 首次使用標記
  static async isFirstTimeUser(): Promise<boolean> {
    try {
      const firstTime = await AsyncStorage.getItem(StorageKeys.FIRST_TIME);
      return firstTime === null;
    } catch (error) {
      console.error('Failed to check first time user:', error);
      return true;
    }
  }

  static async setFirstTimeUser(isFirstTime: boolean): Promise<boolean> {
    try {
      if (isFirstTime) {
        await AsyncStorage.removeItem(StorageKeys.FIRST_TIME);
      } else {
        await AsyncStorage.setItem(StorageKeys.FIRST_TIME, 'false');
      }
      return true;
    } catch (error) {
      console.error('Failed to set first time user:', error);
      return false;
    }
  }

  // 統計數據
  static async getTodayStats(): Promise<TodayStats> {
    try {
      const today = new Date().toDateString();
      const sessions = await this.getSessions();
      const todaySessions = sessions.filter(s => 
        new Date(s.createdAt).toDateString() === today
      );

      return {
        sessionCount: todaySessions.length,
        totalCount: todaySessions.reduce((sum, s) => sum + s.count, 0),
        totalDuration: todaySessions.reduce((sum, s) => sum + (s.duration || 0), 0),
      };
    } catch (error) {
      console.error('Failed to get today stats:', error);
      return { sessionCount: 0, totalCount: 0, totalDuration: 0 };
    }
  }

  static async getProjectStats(projectId: string): Promise<ProjectStats> {
    try {
      const sessions = await this.getSessionsByProject(projectId);
      const totalCount = sessions.reduce((sum, s) => sum + s.count, 0);
      const totalDuration = sessions.reduce((sum, s) => sum + (s.duration || 0), 0);
      const avgSessionCount = sessions.length > 0 ? Math.round(totalCount / sessions.length) : 0;

      return {
        sessionCount: sessions.length,
        totalCount,
        totalDuration,
        avgSessionCount,
        lastSessionAt: sessions.length > 0 ? sessions[sessions.length - 1].createdAt : undefined,
      };
    } catch (error) {
      console.error('Failed to get project stats:', error);
      return { sessionCount: 0, totalCount: 0, totalDuration: 0, avgSessionCount: 0 };
    }
  }

  static async getAllStats(): Promise<AllStats> {
    try {
      const projects = await this.getProjects();
      const sessions = await this.getSessions();
      const wishes = await this.getWishes();
      const blessings = await this.getBlessings();

      return {
        projectCount: projects.length,
        sessionCount: sessions.length,
        totalCount: sessions.reduce((sum, s) => sum + s.count, 0),
        wishCount: wishes.length,
        blessedWishCount: wishes.filter(w => w.blessedCount > 0).length,
        blessingCount: blessings.length,
      };
    } catch (error) {
      console.error('Failed to get all stats:', error);
      return {
        projectCount: 0,
        sessionCount: 0,
        totalCount: 0,
        wishCount: 0,
        blessedWishCount: 0,
        blessingCount: 0,
      };
    }
  }

  // 數據導出和導入
  static async exportData(): Promise<ExportData | null> {
    try {
      const projects = await this.getProjects();
      const sessions = await this.getSessions();
      const wishes = await this.getWishes();
      const blessings = await this.getBlessings();
      const settings = await this.getSettings();

      return {
        projects,
        sessions,
        wishes,
        blessings,
        settings: settings || this.getDefaultSettings(),
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
      };
    } catch (error) {
      console.error('Failed to export data:', error);
      return null;
    }
  }

  static async importData(data: ExportData): Promise<boolean> {
    try {
      if (data.projects) await this.saveProjects(data.projects);
      if (data.sessions) await this.saveSessions(data.sessions);
      if (data.wishes) await this.saveWishes(data.wishes);
      if (data.blessings) await this.saveBlessings(data.blessings);
      if (data.settings) await this.saveSettings(data.settings);
      
      return true;
    } catch (error) {
      console.error('Failed to import data:', error);
      return false;
    }
  }

  // 清除所有數據
  static async clearAllData(): Promise<boolean> {
    try {
      await AsyncStorage.multiRemove([
        StorageKeys.PROJECTS,
        StorageKeys.SESSIONS,
        StorageKeys.WISHES,
        StorageKeys.BLESSINGS,
        StorageKeys.SETTINGS,
        StorageKeys.ACHIEVEMENTS,
      ]);
      
      await this.initializeDefaultData();
      return true;
    } catch (error) {
      console.error('Failed to clear all data:', error);
      return false;
    }
  }

  // 工具方法
  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  static formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}小時${minutes}分鐘`;
    } else if (minutes > 0) {
      return `${minutes}分${secs}秒`;
    } else {
      return `${secs}秒`;
    }
  }

  static formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return '今天';
    } else if (diffDays === 2) {
      return '昨天';
    } else if (diffDays <= 7) {
      return `${diffDays - 1}天前`;
    } else {
      return date.toLocaleDateString('zh-TW');
    }
  }
}
