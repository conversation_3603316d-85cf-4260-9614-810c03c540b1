# 📋 咒語計數器測試指南

本文檔提供完整的測試策略和執行指南，確保應用在上架前達到商業級質量標準。

## 🎯 測試目標

- ✅ **功能完整性** - 所有功能正常工作
- ✅ **性能穩定性** - 應用運行流暢，無崩潰
- ✅ **用戶體驗** - 界面友好，交互直觀
- ✅ **數據安全** - 數據存儲和處理安全
- ✅ **跨平台兼容** - iOS 和 Android 雙平台支持
- ✅ **商店合規** - 符合應用商店審核標準

## 🧪 測試層級

### 1. 單元測試 (Unit Tests)
測試個別組件和函數的功能。

```bash
# 運行所有單元測試
npm test

# 運行測試並生成覆蓋率報告
npm test -- --coverage

# 監視模式運行測試
npm test -- --watch

# 運行特定測試文件
npm test StorageService.test.ts
```

**覆蓋率目標**: 80% 以上

### 2. 集成測試 (Integration Tests)
測試組件之間的交互和數據流。

```bash
# 運行集成測試
npm run test:integration

# 測試特定功能模塊
npm test -- --testNamePattern="Project Management"
```

### 3. E2E 測試 (End-to-End Tests)
測試完整的用戶流程。

```bash
# 安裝 Detox (首次)
npm install -g detox-cli

# 構建測試應用
detox build --configuration ios.sim.debug

# 運行 E2E 測試
detox test --configuration ios.sim.debug

# Android E2E 測試
detox test --configuration android.emu.debug
```

### 4. 性能測試 (Performance Tests)
測試應用性能和資源使用。

```bash
# Bundle 大小分析
npx expo export --platform all
du -sh dist/

# 內存洩漏檢測
npm test -- --detectLeaks

# 啟動時間測試
npm run test:performance
```

## 🚀 快速測試

使用我們提供的測試腳本進行一鍵測試：

```bash
# 賦予執行權限
chmod +x scripts/test.sh

# 運行完整測試套件
./scripts/test.sh
```

## 📱 設備測試

### iOS 測試設備
- **iPhone 12 Mini** (5.4") - 最小屏幕
- **iPhone 14** (6.1") - 標準屏幕
- **iPhone 14 Pro Max** (6.7") - 最大屏幕
- **iPad** (10.9") - 平板設備
- **iPad Pro** (12.9") - 大屏平板

### Android 測試設備
- **小屏手機** (5.0" - 5.5")
- **標準手機** (6.0" - 6.5")
- **大屏手機** (6.5"+)
- **7 吋平板**
- **10 吋平板**

### 系統版本
- **iOS**: 13.0+ (支持的最低版本)
- **Android**: API 21+ (Android 5.0+)

## 🧩 功能測試清單

### 核心功能
- [ ] **項目管理**
  - [ ] 創建新項目
  - [ ] 編輯項目信息
  - [ ] 刪除項目
  - [ ] 項目進度顯示
  - [ ] 項目統計數據

- [ ] **計數功能**
  - [ ] 基本計數操作
  - [ ] 快速操作 (+10, -1)
  - [ ] 暫停/恢復功能
  - [ ] 重置功能
  - [ ] 進度環顯示
  - [ ] 時間統計

- [ ] **願望清單**
  - [ ] 添加新願望
  - [ ] 編輯願望
  - [ ] 刪除願望
  - [ ] 願望分類
  - [ ] 篩選功能
  - [ ] 祝福計數

- [ ] **功德迴向**
  - [ ] 迴向文編輯
  - [ ] 模板選擇
  - [ ] 願望選擇
  - [ ] 功德灌注
  - [ ] 社群分享

### 用戶體驗
- [ ] **導航**
  - [ ] 頁面切換流暢
  - [ ] 返回按鈕正常
  - [ ] 深度鏈接支持
  - [ ] 狀態保持

- [ ] **交互反饋**
  - [ ] 按鈕點擊反饋
  - [ ] 音效播放
  - [ ] 觸覺反饋
  - [ ] 動畫效果
  - [ ] 加載狀態

- [ ] **錯誤處理**
  - [ ] 網絡錯誤處理
  - [ ] 數據錯誤處理
  - [ ] 用戶輸入驗證
  - [ ] 友好錯誤提示

### 數據管理
- [ ] **本地存儲**
  - [ ] 數據持久化
  - [ ] 數據恢復
  - [ ] 數據遷移
  - [ ] 存儲空間管理

- [ ] **數據同步**
  - [ ] 跨頁面數據同步
  - [ ] 實時更新
  - [ ] 數據一致性
  - [ ] 衝突解決

## 🔧 測試工具配置

### Jest 配置
```javascript
// jest.config.js
module.exports = {
  preset: 'jest-expo',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test/**/*',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### Detox 配置
```javascript
// .detoxrc.js
module.exports = {
  testRunner: {
    args: {
      '$0': 'jest',
      config: 'e2e/jest.config.js'
    }
  },
  apps: {
    'ios.debug': {
      type: 'ios.app',
      binaryPath: 'ios/build/Build/Products/Debug-iphonesimulator/MantraCounter.app'
    }
  },
  devices: {
    simulator: {
      type: 'ios.simulator',
      device: { type: 'iPhone 14' }
    }
  }
};
```

## 📊 測試報告

### 覆蓋率報告
測試完成後，覆蓋率報告將生成在 `coverage/` 目錄：
- `coverage/lcov-report/index.html` - HTML 報告
- `coverage/lcov.info` - LCOV 格式報告
- `coverage/coverage-final.json` - JSON 格式報告

### E2E 測試報告
E2E 測試結果將保存在 `e2e/artifacts/` 目錄：
- 測試截圖
- 測試視頻
- 設備日誌
- 性能數據

## 🚨 常見問題解決

### 測試環境問題

**問題**: Jest 測試運行失敗
```bash
# 清除 Jest 緩存
npx jest --clearCache

# 重新安裝依賴
rm -rf node_modules package-lock.json
npm install
```

**問題**: Detox 測試無法啟動
```bash
# 重新構建測試應用
detox clean-framework-cache && detox build-framework-cache

# 檢查模擬器狀態
xcrun simctl list devices
```

**問題**: Metro bundler 錯誤
```bash
# 清除 Metro 緩存
npx expo start --clear

# 重置 Metro 配置
rm -rf .expo
```

### 性能問題

**問題**: 測試運行緩慢
- 使用 `--maxWorkers=1` 限制並發
- 增加測試超時時間
- 分批運行測試

**問題**: 內存不足
- 關閉不必要的應用
- 增加 Node.js 內存限制：`--max-old-space-size=4096`

## 📋 上架前檢查清單

### 功能完整性
- [ ] 所有核心功能正常工作
- [ ] 用戶流程完整無阻斷
- [ ] 錯誤處理完善
- [ ] 性能表現良好

### 用戶體驗
- [ ] 界面美觀一致
- [ ] 交互流暢自然
- [ ] 反饋及時明確
- [ ] 無障礙支持

### 技術質量
- [ ] 代碼質量良好
- [ ] 測試覆蓋率達標
- [ ] 性能指標合格
- [ ] 安全檢查通過

### 商店合規
- [ ] 應用圖標和截圖準備完成
- [ ] 應用描述和關鍵詞優化
- [ ] 隱私政策和使用條款完整
- [ ] 內容分級正確

## 🔄 持續集成

### GitHub Actions 配置
```yaml
# .github/workflows/test.yml
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test -- --coverage
      - run: npm run build
```

### 自動化測試觸發
- **代碼提交時** - 運行單元測試和集成測試
- **Pull Request** - 運行完整測試套件
- **發布前** - 運行所有測試包括 E2E 測試
- **定期檢查** - 每日運行回歸測試

## 📞 測試支援

如果在測試過程中遇到問題，請：

1. **查看文檔** - 首先查看相關測試文檔
2. **檢查日誌** - 查看詳細的錯誤日誌
3. **搜索問題** - 在 GitHub Issues 中搜索類似問題
4. **提交 Issue** - 如果問題未解決，請提交詳細的 Issue

---

**記住**: 充分的測試是確保應用質量的關鍵。投入時間進行測試將大大減少上架後的問題和用戶投訴。
