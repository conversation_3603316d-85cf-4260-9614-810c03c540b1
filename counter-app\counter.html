<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修行計數 - 咒語計數器</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/counter.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="counter-container">
        <!-- 頂部信息區 -->
        <header class="counter-header">
            <button class="back-btn" onclick="exitCounter()">
                <svg viewBox="0 0 24 24">
                    <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                </svg>
            </button>
            <div class="header-info">
                <h1 class="project-title" id="counterProjectTitle">六字大明咒</h1>
                <div class="progress-info">
                    <span class="total-progress" id="totalProgress">520 / 1,000,000</span>
                    <span class="progress-percentage" id="progressPercentage">0.052%</span>
                </div>
            </div>
            <button class="pause-btn" onclick="pauseCounter()">
                <svg viewBox="0 0 24 24">
                    <path d="M14,19H18V5H14M6,19H10V5H6V19Z"/>
                </svg>
            </button>
        </header>

        <!-- 主計數區域 -->
        <main class="counter-main">
            <!-- 目標進度環 -->
            <div class="target-ring">
                <svg class="ring-svg" viewBox="0 0 200 200">
                    <defs>
                        <linearGradient id="ringGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#92A3FD;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#9DCEFF;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <circle cx="100" cy="100" r="90" class="ring-bg"/>
                    <circle cx="100" cy="100" r="90" class="ring-progress" id="sessionRing"/>
                </svg>
                <div class="ring-content">
                    <div class="session-count" id="sessionCount">0</div>
                    <div class="session-target">/ <span id="sessionTarget">108</span></div>
                </div>
            </div>

            <!-- 計數按鈕 -->
            <div class="counter-button-area">
                <button class="counter-btn" id="counterBtn" ontouchstart="handleCounterTouch()" ontouchend="handleCounterRelease()" onclick="incrementCounter()">
                    <div class="btn-content">
                        <div class="btn-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
                            </svg>
                        </div>
                        <span class="btn-text">點擊持誦</span>
                    </div>
                    <div class="btn-ripple" id="btnRipple"></div>
                </button>
            </div>

            <!-- 快速操作 -->
            <div class="quick-actions">
                <button class="quick-action-btn" onclick="addTen()">
                    <span>+10</span>
                </button>
                <button class="quick-action-btn" onclick="subtractOne()" id="subtractBtn" disabled>
                    <span>-1</span>
                </button>
                <button class="quick-action-btn" onclick="resetSession()">
                    <svg viewBox="0 0 24 24">
                        <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                    </svg>
                </button>
            </div>
        </main>

        <!-- 底部狀態 -->
        <footer class="counter-footer">
            <div class="session-info">
                <div class="info-item">
                    <span class="info-label">開始時間</span>
                    <span class="info-value" id="startTime">--:--</span>
                </div>
                <div class="info-item">
                    <span class="info-label">持續時間</span>
                    <span class="info-value" id="duration">00:00</span>
                </div>
                <div class="info-item">
                    <span class="info-label">平均速度</span>
                    <span class="info-value" id="avgSpeed">-- /分</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 完成慶祝模態框 -->
    <div class="modal-overlay celebration-modal" id="celebrationModal" style="display: none;">
        <div class="modal-content celebration-content">
            <div class="celebration-icon">
                <svg viewBox="0 0 24 24">
                    <path d="M12,2L13.09,8.26L22,9L14.74,13.74L17.18,22L12,17.27L6.82,22L9.26,13.74L2,9L10.91,8.26L12,2Z"/>
                </svg>
            </div>
            <h3>🎉 修行完成！</h3>
            <div class="completion-stats">
                <p>恭喜您完成了本次修行會期</p>
                <div class="stats-row">
                    <span>持誦次數：<strong id="completedCount">108</strong></span>
                </div>
                <div class="stats-row">
                    <span>用時：<strong id="completedDuration">5分30秒</strong></span>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="modal-btn primary" onclick="proceedToMerit()">迴向功德</button>
                <button class="modal-btn secondary" onclick="continueCounting()">繼續修行</button>
            </div>
        </div>
    </div>

    <!-- 暫停模態框 -->
    <div class="modal-overlay" id="pauseModal" style="display: none;">
        <div class="modal-content">
            <h3>暫停修行</h3>
            <p>您確定要暫停當前的修行會期嗎？</p>
            <div class="pause-stats">
                <p>當前進度：<span id="pauseProgress">25 / 108</span></p>
                <p>已用時間：<span id="pauseDuration">2分30秒</span></p>
            </div>
            <div class="modal-buttons">
                <button class="modal-btn secondary" onclick="resumeCounter()">繼續修行</button>
                <button class="modal-btn primary" onclick="saveAndExit()">保存並退出</button>
            </div>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script src="js/counter.js"></script>
</body>
</html>
