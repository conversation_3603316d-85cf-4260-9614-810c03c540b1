import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Colors } from '../constants/Colors';
import { AudioService } from '../services/AudioService';

interface CreateProjectModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: {
    title: string;
    description: string;
    totalGoalCount: number;
  }) => void;
}

const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  visible,
  onClose,
  onSubmit,
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [goalCount, setGoalCount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const quickGoals = [
    { label: '10萬', value: 100000 },
    { label: '50萬', value: 500000 },
    { label: '100萬', value: 1000000 },
    { label: '500萬', value: 5000000 },
  ];

  const handleClose = async () => {
    await AudioService.buttonPress();
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setGoalCount('');
    setIsSubmitting(false);
  };

  const handleQuickGoal = async (value: number) => {
    await AudioService.buttonPress();
    setGoalCount(value.toString());
  };

  const validateForm = () => {
    if (!title.trim()) {
      Alert.alert('錯誤', '請輸入項目名稱');
      return false;
    }

    const goal = parseInt(goalCount);
    if (!goal || goal <= 0) {
      Alert.alert('錯誤', '請輸入有效的目標數量');
      return false;
    }

    if (goal > 10000000) {
      Alert.alert('錯誤', '目標數量不能超過一千萬');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    await AudioService.buttonPress();

    try {
      onSubmit({
        title: title.trim(),
        description: description.trim(),
        totalGoalCount: parseInt(goalCount),
      });
      resetForm();
    } catch (error) {
      console.error('Failed to submit project:', error);
      Alert.alert('錯誤', '創建項目失敗，請重試');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={styles.keyboardView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* 頂部導航 */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleClose}
            >
              <Ionicons name="close" size={24} color={Colors.textSecondary} />
            </TouchableOpacity>
            
            <Text style={styles.headerTitle}>創建修行項目</Text>
            
            <TouchableOpacity
              style={[
                styles.submitButton,
                (!title.trim() || !goalCount) && styles.submitButtonDisabled,
              ]}
              onPress={handleSubmit}
              disabled={!title.trim() || !goalCount || isSubmitting}
            >
              <Text
                style={[
                  styles.submitButtonText,
                  (!title.trim() || !goalCount) && styles.submitButtonTextDisabled,
                ]}
              >
                {isSubmitting ? '創建中...' : '創建'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* 項目名稱 */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>項目名稱 *</Text>
              <TextInput
                style={styles.textInput}
                value={title}
                onChangeText={setTitle}
                placeholder="例如：六字大明咒"
                placeholderTextColor={Colors.textTertiary}
                maxLength={50}
                returnKeyType="next"
              />
              <Text style={styles.charCount}>{title.length}/50</Text>
            </View>

            {/* 目標數量 */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>目標數量 *</Text>
              <TextInput
                style={styles.textInput}
                value={goalCount}
                onChangeText={setGoalCount}
                placeholder="例如：1000000"
                placeholderTextColor={Colors.textTertiary}
                keyboardType="numeric"
                maxLength={8}
                returnKeyType="next"
              />
              
              {/* 快速選擇 */}
              <View style={styles.quickGoalsContainer}>
                <Text style={styles.quickGoalsLabel}>快速選擇：</Text>
                <View style={styles.quickGoalsGrid}>
                  {quickGoals.map((goal) => (
                    <TouchableOpacity
                      key={goal.value}
                      style={[
                        styles.quickGoalButton,
                        goalCount === goal.value.toString() && styles.quickGoalButtonActive,
                      ]}
                      onPress={() => handleQuickGoal(goal.value)}
                    >
                      <Text
                        style={[
                          styles.quickGoalText,
                          goalCount === goal.value.toString() && styles.quickGoalTextActive,
                        ]}
                      >
                        {goal.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>

            {/* 項目描述 */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>項目描述（可選）</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="為此修行項目添加描述..."
                placeholderTextColor={Colors.textTertiary}
                multiline
                numberOfLines={4}
                maxLength={200}
                textAlignVertical="top"
              />
              <Text style={styles.charCount}>{description.length}/200</Text>
            </View>

            {/* 提示信息 */}
            <View style={styles.tipContainer}>
              <LinearGradient
                colors={[Colors.primaryLight, Colors.secondaryLight]}
                style={styles.tipGradient}
              >
                <Ionicons
                  name="bulb-outline"
                  size={20}
                  color={Colors.primary}
                  style={styles.tipIcon}
                />
                <View style={styles.tipContent}>
                  <Text style={styles.tipTitle}>修行建議</Text>
                  <Text style={styles.tipText}>
                    建議設定一個具有挑戰性但可達成的目標，比如十萬遍或百萬遍。
                    持之以恆的修行比一次性的大量持誦更有意義。
                  </Text>
                </View>
              </LinearGradient>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  submitButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.primary,
  },
  submitButtonDisabled: {
    backgroundColor: Colors.gray200,
  },
  submitButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
  },
  submitButtonTextDisabled: {
    color: Colors.textTertiary,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  formGroup: {
    marginTop: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 2,
    borderColor: Colors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.textPrimary,
    backgroundColor: Colors.white,
  },
  textArea: {
    height: 100,
    paddingTop: 12,
  },
  charCount: {
    fontSize: 12,
    color: Colors.textTertiary,
    textAlign: 'right',
    marginTop: 4,
  },
  quickGoalsContainer: {
    marginTop: 12,
  },
  quickGoalsLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  quickGoalsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickGoalButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.white,
  },
  quickGoalButtonActive: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primaryLight,
  },
  quickGoalText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  quickGoalTextActive: {
    color: Colors.primary,
  },
  tipContainer: {
    marginTop: 32,
    marginBottom: 24,
  },
  tipGradient: {
    borderRadius: 12,
    padding: 16,
  },
  tipIcon: {
    marginBottom: 8,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: 4,
  },
  tipText: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textSecondary,
    lineHeight: 18,
  },
});

export default CreateProjectModal;
