// 修行項目類型
export interface Project {
  id: string;
  title: string;
  description?: string;
  totalGoalCount: number;
  currentTotalCount: number;
  createdAt: string;
  updatedAt: string;
}

// 修行會期類型
export interface Session {
  id: string;
  projectId: string;
  count: number;
  target: number;
  duration: number; // 秒數
  startTime: string;
  endTime: string;
  completed: boolean;
  createdAt: string;
}

// 願望類型
export interface Wish {
  id: string;
  text: string;
  category?: WishCategory;
  blessedCount: number;
  lastBlessedAt?: string;
  createdAt: string;
}

// 願望分類
export type WishCategory = 'health' | 'family' | 'career' | 'study' | 'peace' | 'other';

// 祝福記錄類型
export interface Blessing {
  id: string;
  projectId: string;
  count: number;
  wishIds: string[];
  dedicationText: string;
  sessionData: Partial<Session>;
  createdAt: string;
}

// 應用設置類型
export interface AppSettings {
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  theme: 'light' | 'dark';
  language: 'zh-TW' | 'zh-CN' | 'en';
  autoSave: boolean;
  reminderEnabled: boolean;
  reminderTime?: string;
}

// 統計數據類型
export interface TodayStats {
  sessionCount: number;
  totalCount: number;
  totalDuration: number;
}

export interface ProjectStats {
  sessionCount: number;
  totalCount: number;
  totalDuration: number;
  avgSessionCount: number;
  lastSessionAt?: string;
}

export interface AllStats {
  projectCount: number;
  sessionCount: number;
  totalCount: number;
  wishCount: number;
  blessedWishCount: number;
  blessingCount: number;
}

// 迴向文模板類型
export interface DedicationTemplate {
  id: string;
  name: string;
  content: string;
  category: 'universal' | 'family' | 'health' | 'custom';
}

// 會期狀態類型
export interface SessionState {
  isActive: boolean;
  projectId?: string;
  projectTitle?: string;
  target?: number;
  currentCount: number;
  startTime?: Date;
  isPaused: boolean;
}

// API 響應類型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分享內容類型
export interface ShareContent {
  title: string;
  message: string;
  url?: string;
}

// 通知類型
export interface Notification {
  id: string;
  title: string;
  body: string;
  data?: any;
  scheduledTime?: Date;
  type: 'reminder' | 'achievement' | 'milestone';
}

// 成就類型
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  condition: {
    type: 'count' | 'sessions' | 'days' | 'projects';
    value: number;
  };
  unlockedAt?: string;
}

// 導出數據類型
export interface ExportData {
  projects: Project[];
  sessions: Session[];
  wishes: Wish[];
  blessings: Blessing[];
  settings: AppSettings;
  exportedAt: string;
  version: string;
}

// 錯誤類型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// 主題顏色類型
export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  success: string;
  warning: string;
  error: string;
  white: string;
  black: string;
  gray50: string;
  gray100: string;
  gray200: string;
  gray300: string;
  gray400: string;
  gray500: string;
  gradientPrimary: string[];
  gradientSecondary: string[];
  gradientSuccess: string[];
}

// 動畫配置類型
export interface AnimationConfig {
  duration: number;
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
}

// 觸覺反饋類型
export type HapticFeedbackType = 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error';

// 音效類型
export type SoundType = 'click' | 'success' | 'complete' | 'error' | 'notification';

// 存儲鍵類型
export enum StorageKeys {
  PROJECTS = 'mantra_projects',
  SESSIONS = 'mantra_sessions',
  WISHES = 'mantra_wishes',
  BLESSINGS = 'mantra_blessings',
  SETTINGS = 'mantra_settings',
  FIRST_TIME = 'mantra_first_time',
  ACHIEVEMENTS = 'mantra_achievements',
  LAST_BACKUP = 'mantra_last_backup',
}
