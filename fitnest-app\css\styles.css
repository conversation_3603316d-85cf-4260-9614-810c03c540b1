/* 基礎重置和變量 */
:root {
    /* 主要顏色 */
    --primary-color: #92A3FD;
    --secondary-color: #9DCEFF;
    --accent-color: #C58BF2;
    --success-color: #42D742;
    --warning-color: #FFD600;
    --error-color: #FF6B6B;
    
    /* 中性顏色 */
    --white: #FFFFFF;
    --gray-50: #F7F8F8;
    --gray-100: #E6EAEE;
    --gray-200: #DDDADA;
    --gray-300: #ADA4A5;
    --gray-400: #7B6F72;
    --gray-500: #1D1617;
    --black: #000000;
    
    /* 漸變 */
    --gradient-primary: linear-gradient(315deg, #92A3FD 0%, #9DCEFF 100%);
    --gradient-secondary: linear-gradient(315deg, #C58BF2 0%, #EEA4CE 100%);
    --gradient-card: linear-gradient(315deg, rgba(146, 163, 253, 0.2) 0%, rgba(157, 206, 255, 0.2) 100%);
    
    /* 字體 */
    --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    
    /* 間距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* 圓角 */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50%;
    
    /* 陰影 */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--white);
    color: var(--gray-500);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 應用容器 */
.app-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background-color: var(--white);
    position: relative;
    overflow-x: hidden;
}

/* 頂部導航 */
.header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
    background-color: var(--white);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-title {
    font-size: 24px;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.icon-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-50);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.icon-btn:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.icon-btn .icon {
    width: 20px;
    height: 20px;
    fill: var(--gray-400);
}

.profile-avatar {
    width: 32px;
    height: 32px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

/* 主要內容 */
.main-content {
    padding: 0 var(--spacing-lg) 100px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* 歡迎橫幅 */
.welcome-banner {
    background: var(--gradient-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.banner-content h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--gray-500);
}

.banner-content p {
    font-size: 14px;
    color: var(--gray-400);
}

.banner-graphic {
    width: 80px;
    height: 80px;
    position: relative;
}

.activity-chart {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    position: relative;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 今日目標 */
.today-target h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--gray-500);
}

.target-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.target-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.target-label {
    font-size: 14px;
    color: var(--gray-400);
}

.target-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
}

.progress-ring {
    position: relative;
    width: 60px;
    height: 60px;
}

.progress-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-bg {
    fill: none;
    stroke: var(--gray-100);
    stroke-width: 8;
}

.progress-fill {
    fill: none;
    stroke: url(#gradient);
    stroke-width: 8;
    stroke-linecap: round;
    stroke-dasharray: 283;
    stroke-dashoffset: calc(283 - (283 * var(--progress)) / 100);
    transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
}

/* 狀態網格 */
.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.status-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.card-title {
    font-size: 12px;
    color: var(--gray-400);
    font-weight: 500;
}

.card-icon {
    width: 16px;
    height: 16px;
    fill: var(--gray-300);
}

.card-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-md);
}

.unit {
    font-size: 12px;
    color: var(--gray-400);
    font-weight: 400;
}

/* 心率卡片 */
.heart-rate .card-icon {
    fill: var(--error-color);
}

.heart-chart {
    height: 40px;
    background: linear-gradient(90deg, transparent 0%, var(--error-color) 50%, transparent 100%);
    border-radius: var(--radius-sm);
    opacity: 0.2;
}

/* 水分攝取卡片 */
.water-intake .card-icon {
    fill: var(--primary-color);
}

.water-progress {
    height: 40px;
    background: var(--gray-100);
    border-radius: var(--radius-sm);
    position: relative;
    overflow: hidden;
}

.water-fill {
    height: 100%;
    width: var(--fill);
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    transition: width 0.5s ease;
}

/* 睡眠卡片 */
.sleep .card-icon {
    fill: var(--accent-color);
}

.sleep-chart {
    height: 40px;
    background: linear-gradient(90deg, var(--accent-color) 0%, transparent 50%, var(--accent-color) 100%);
    border-radius: var(--radius-sm);
    opacity: 0.2;
}

/* 卡路里卡片 */
.calories .card-icon {
    fill: var(--warning-color);
}

.calories-chart {
    height: 40px;
    background: radial-gradient(circle, var(--warning-color) 0%, transparent 70%);
    border-radius: var(--radius-sm);
    opacity: 0.2;
}

/* 最新運動 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
}

.see-more-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

.workout-cards {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.workout-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.workout-card.active {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.workout-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.workout-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    flex-shrink: 0;
}

.fullbody-bg {
    background: var(--gradient-primary);
}

.lowerbody-bg {
    background: var(--gradient-secondary);
}

.ab-bg {
    background: linear-gradient(315deg, var(--success-color) 0%, #7ED321 100%);
}

.workout-info {
    flex: 1;
}

.workout-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-xs);
}

.workout-info p {
    font-size: 12px;
    color: var(--gray-400);
}

.workout-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.workout-action-btn:hover {
    transform: scale(1.1);
}

.workout-action-btn svg {
    width: 16px;
    height: 16px;
    fill: var(--white);
}

/* 底部導航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 375px;
    background: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 100;
}

.nav-item {
    background: none;
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    transition: all 0.2s ease;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
}

.nav-item.active {
    background: var(--gradient-card);
}

.nav-icon {
    width: 24px;
    height: 24px;
    fill: var(--gray-300);
    transition: fill 0.2s ease;
}

.nav-item.active .nav-icon {
    fill: var(--primary-color);
}

.nav-label {
    font-size: 10px;
    color: var(--gray-300);
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-item.active .nav-label {
    color: var(--primary-color);
}

/* 模態框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin: var(--spacing-lg);
    max-width: 320px;
    width: 100%;
    text-align: center;
    animation: slideUp 0.3s ease;
}

.modal-content h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-md);
}

.modal-content p {
    font-size: 14px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-xl);
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.modal-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.modal-btn:first-child {
    background: var(--gray-100);
    color: var(--gray-400);
}

.modal-btn:first-child:hover {
    background: var(--gray-200);
}

.modal-btn:last-child {
    background: var(--gradient-primary);
    color: var(--white);
}

.modal-btn:last-child:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 提示框 */
.toast {
    position: fixed;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background: var(--gray-500);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    z-index: 1001;
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 280px;
    text-align: center;
}

.toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

/* 動畫 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* SVG 漸變定義 */
.progress-svg defs {
    display: none;
}

/* 響應式設計 */
@media (max-width: 320px) {
    .app-container {
        max-width: 100%;
    }

    .header,
    .main-content {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .status-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: var(--spacing-md);
        padding: var(--spacing-lg);
    }
}

@media (min-width: 768px) {
    .app-container {
        max-width: 768px;
    }

    .status-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .workout-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }

    .modal-content {
        max-width: 400px;
    }
}
