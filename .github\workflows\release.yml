# Originally inspired by create-t3-app
# https://github.com/t3-oss/create-t3-app/blob/main/.github/workflows/release.yml

name: Release

on:
  push:
    branches:
      - main

jobs:
  release:
    if: ${{ github.repository_owner == 'G<PERSON><PERSON><PERSON>' }}
    name: Create a PR for release workflow
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup

      - name: Check for errors
        run: pnpm type-check

      - name: Build the package
        run: pnpm build

      - name: Create Version PR or Publish to NPM
        id: changesets
        uses: changesets/action@v1
        with:
          commit: "chore(release): version packages"
          title: "chore(release): version packages"
          version: node .github/changeset-version.js
          publish: npx changeset publish
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_ACCESS_TOKEN }}
          NODE_ENV: "production"
