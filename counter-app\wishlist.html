<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>願望清單 - 咒語計數器</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/wishlist.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 頂部導航 -->
        <header class="header">
            <div class="header-content">
                <button class="back-btn" onclick="goBack()">
                    <svg viewBox="0 0 24 24">
                        <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                    </svg>
                </button>
                <h1 class="page-title">願望清單</h1>
                <button class="icon-btn add-btn" onclick="showAddWish()">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                    </svg>
                </button>
            </div>
        </header>

        <!-- 主要內容 -->
        <main class="main-content">
            <!-- 願望統計 -->
            <section class="wish-stats">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.04L12,21.35Z"/>
                            </svg>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="totalWishes">0</span>
                            <span class="stat-label">總願望數</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon blessed">
                            <svg viewBox="0 0 24 24">
                                <path d="M12,2L13.09,8.26L22,9L14.74,13.74L17.18,22L12,17.27L6.82,22L9.26,13.74L2,9L10.91,8.26L12,2Z"/>
                            </svg>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="blessedWishes">0</span>
                            <span class="stat-label">已祝福</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 願望列表 -->
            <section class="wishes-section">
                <div class="section-header">
                    <h3>我的願望</h3>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">全部</button>
                        <button class="filter-btn" data-filter="blessed">已祝福</button>
                        <button class="filter-btn" data-filter="pending">待祝福</button>
                    </div>
                </div>
                
                <div class="wishes-list" id="wishesList">
                    <!-- 願望項目將通過 JavaScript 動態生成 -->
                </div>

                <!-- 空狀態 -->
                <div class="empty-state" id="wishesEmptyState" style="display: none;">
                    <div class="empty-icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.04L12,21.35Z"/>
                        </svg>
                    </div>
                    <h4>還沒有願望</h4>
                    <p>點擊右上角的 + 號添加您的第一個願望</p>
                    <button class="primary-btn" onclick="showAddWish()">添加願望</button>
                </div>
            </section>

            <!-- 祝福記錄 -->
            <section class="blessing-records">
                <div class="section-header">
                    <h3>祝福記錄</h3>
                    <button class="see-more-btn" onclick="showAllRecords()">查看全部</button>
                </div>
                
                <div class="records-list" id="recordsList">
                    <!-- 祝福記錄將通過 JavaScript 動態生成 -->
                </div>

                <!-- 記錄空狀態 -->
                <div class="empty-state small" id="recordsEmptyState" style="display: none;">
                    <p>還沒有祝福記錄</p>
                </div>
            </section>
        </main>

        <!-- 底部導航 -->
        <nav class="bottom-nav">
            <button class="nav-item" onclick="goHome()">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"/>
                </svg>
                <span class="nav-label">首頁</span>
            </button>
            <button class="nav-item active">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.04L12,21.35Z"/>
                </svg>
                <span class="nav-label">願望清單</span>
            </button>
            <button class="nav-item" onclick="showHistory()">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3"/>
                </svg>
                <span class="nav-label">歷史</span>
            </button>
            <button class="nav-item" onclick="showStats()">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
                </svg>
                <span class="nav-label">統計</span>
            </button>
        </nav>
    </div>

    <!-- 添加願望模態框 -->
    <div class="modal-overlay" id="addWishModal" style="display: none;">
        <div class="modal-content">
            <h3>添加願望</h3>
            <form id="addWishForm">
                <div class="form-group">
                    <label for="wishText">願望內容</label>
                    <textarea id="wishText" placeholder="寫下您的願望..." required></textarea>
                </div>
                <div class="form-group">
                    <label for="wishCategory">願望類別（可選）</label>
                    <select id="wishCategory">
                        <option value="">選擇類別</option>
                        <option value="health">健康</option>
                        <option value="family">家庭</option>
                        <option value="career">事業</option>
                        <option value="study">學業</option>
                        <option value="peace">平安</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="modal-buttons">
                    <button type="button" class="modal-btn secondary" onclick="hideAddWish()">取消</button>
                    <button type="submit" class="modal-btn primary">添加</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 編輯願望模態框 -->
    <div class="modal-overlay" id="editWishModal" style="display: none;">
        <div class="modal-content">
            <h3>編輯願望</h3>
            <form id="editWishForm">
                <div class="form-group">
                    <label for="editWishText">願望內容</label>
                    <textarea id="editWishText" required></textarea>
                </div>
                <div class="form-group">
                    <label for="editWishCategory">願望類別</label>
                    <select id="editWishCategory">
                        <option value="">選擇類別</option>
                        <option value="health">健康</option>
                        <option value="family">家庭</option>
                        <option value="career">事業</option>
                        <option value="study">學業</option>
                        <option value="peace">平安</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="modal-buttons">
                    <button type="button" class="modal-btn danger" onclick="deleteWish()">刪除</button>
                    <button type="button" class="modal-btn secondary" onclick="hideEditWish()">取消</button>
                    <button type="submit" class="modal-btn primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 祝福記錄詳情模態框 -->
    <div class="modal-overlay" id="recordDetailModal" style="display: none;">
        <div class="modal-content">
            <h3>祝福記錄詳情</h3>
            <div class="record-detail" id="recordDetail">
                <!-- 記錄詳情將通過 JavaScript 動態生成 -->
            </div>
            <div class="modal-buttons">
                <button class="modal-btn primary" onclick="hideRecordDetail()">關閉</button>
            </div>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script src="js/wishlist.js"></script>
</body>
</html>
