/* 願望清單頁面專用樣式 */

/* 頁面標題和返回按鈕 */
.back-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-50);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.back-btn svg {
    width: 20px;
    height: 20px;
    fill: var(--gray-400);
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-500);
}

/* 願望統計 */
.wish-stats {
    margin-bottom: var(--spacing-xl);
}

.wish-stats .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.wish-stats .stat-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.wish-stats .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-secondary);
    flex-shrink: 0;
}

.wish-stats .stat-icon.blessed {
    background: var(--gradient-success);
}

.wish-stats .stat-icon svg {
    width: 24px;
    height: 24px;
    fill: var(--white);
}

/* 篩選按鈕 */
.filter-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.filter-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    border: none;
    background: var(--gray-100);
    color: var(--gray-400);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn.active {
    background: var(--gradient-primary);
    color: var(--white);
}

.filter-btn:hover:not(.active) {
    background: var(--gray-200);
}

/* 願望列表 */
.wishes-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.wish-item {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    position: relative;
    transition: all 0.2s ease;
    cursor: pointer;
}

.wish-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.wish-item.blessed {
    border-left: 4px solid var(--success-color);
}

.wish-item.blessed::after {
    content: '';
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 20px;
    height: 20px;
    background: var(--success-color);
    border-radius: var(--radius-full);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='white' d='M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z'/%3E%3C/svg%3E");
    background-size: 12px;
    background-repeat: no-repeat;
    background-position: center;
}

.wish-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.wish-category {
    font-size: 10px;
    color: var(--primary-color);
    background: var(--gradient-card);
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.wish-text {
    font-size: 14px;
    color: var(--gray-500);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
}

.wish-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--gray-400);
}

.wish-date {
    font-weight: 500;
}

.wish-blessing-count {
    color: var(--success-color);
    font-weight: 600;
}

.wish-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.wish-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: var(--gray-100);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.wish-action-btn:hover {
    background: var(--gray-200);
}

.wish-action-btn svg {
    width: 14px;
    height: 14px;
    fill: var(--gray-400);
}

/* 祝福記錄 */
.blessing-records {
    margin-bottom: var(--spacing-xl);
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.record-item {
    background: var(--white);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.record-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.record-project {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-500);
}

.record-count {
    font-size: 12px;
    color: var(--primary-color);
    background: var(--gradient-card);
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 600;
}

.record-wishes {
    font-size: 12px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-sm);
}

.record-date {
    font-size: 10px;
    color: var(--gray-300);
    font-weight: 500;
}

/* 記錄詳情 */
.record-detail {
    text-align: left;
}

.record-detail h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-md);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--gray-100);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 14px;
    color: var(--gray-400);
}

.detail-value {
    font-size: 14px;
    color: var(--gray-500);
    font-weight: 500;
}

.blessed-wishes-list {
    margin-top: var(--spacing-md);
}

.blessed-wish-item {
    background: var(--gray-50);
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: 12px;
    color: var(--gray-500);
}

.blessed-wish-item:last-child {
    margin-bottom: 0;
}

/* 表單樣式擴展 */
.form-group select {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-100);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-family: var(--font-family);
    background: var(--white);
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* 危險按鈕 */
.modal-btn.danger {
    background: var(--error-color);
    color: var(--white);
}

.modal-btn.danger:hover {
    background: #e55555;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 小型空狀態 */
.empty-state.small {
    padding: var(--spacing-lg);
    text-align: center;
    background: var(--gray-50);
    border-radius: var(--radius-md);
}

.empty-state.small p {
    font-size: 14px;
    color: var(--gray-400);
    margin: 0;
}

/* 動畫效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.wish-item,
.record-item {
    animation: slideInUp 0.4s ease forwards;
}

.wish-item:nth-child(1) { animation-delay: 0.1s; }
.wish-item:nth-child(2) { animation-delay: 0.2s; }
.wish-item:nth-child(3) { animation-delay: 0.3s; }
.wish-item:nth-child(4) { animation-delay: 0.4s; }
.wish-item:nth-child(5) { animation-delay: 0.5s; }

/* 響應式設計 */
@media (max-width: 320px) {
    .wish-stats .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-buttons {
        flex-wrap: wrap;
    }
    
    .wish-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

@media (min-width: 768px) {
    .wish-stats .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .wishes-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }
}
