import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
  BackHandler,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import Svg, { Circle } from 'react-native-svg';

import { Colors } from '../constants/Colors';
import { AudioService } from '../services/AudioService';
import { StorageService } from '../services/StorageService';
import { RootStackScreenProps } from '../types/navigation';
import { Project, ProjectStats } from '../types';

const { width, height } = Dimensions.get('window');
const COUNTER_SIZE = Math.min(width * 0.6, 240);
const RING_SIZE = Math.min(width * 0.8, 320);

type CounterScreenProps = RootStackScreenProps<'Counter'>;

const CounterScreen: React.FC<CounterScreenProps> = ({ navigation, route }) => {
  const { projectId, projectTitle, target } = route.params;
  
  const [currentCount, setCurrentCount] = useState(0);
  const [startTime] = useState(new Date());
  const [duration, setDuration] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [project, setProject] = useState<Project | null>(null);
  const [totalStats, setTotalStats] = useState<ProjectStats | null>(null);

  // 動畫值
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rippleAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  // 計時器
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadProjectData();
    startTimer();
    setupBackHandler();

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    updateProgressAnimation();
  }, [currentCount, target]);

  const loadProjectData = async () => {
    try {
      const [projectData, statsData] = await Promise.all([
        StorageService.getProject(projectId),
        StorageService.getProjectStats(projectId),
      ]);
      setProject(projectData);
      setTotalStats(statsData);
    } catch (error) {
      console.error('Failed to load project data:', error);
    }
  };

  const startTimer = () => {
    timerRef.current = setInterval(() => {
      if (!isPaused) {
        setDuration(prev => prev + 1);
      }
    }, 1000);
  };

  const setupBackHandler = () => {
    const backAction = () => {
      handleExit();
      return true;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  };

  const updateProgressAnimation = () => {
    const progress = target > 0 ? currentCount / target : 0;
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 500,
      useNativeDriver: false,
    }).start();
  };

  const handleCounterPress = async () => {
    if (isPaused) return;

    // 音效和觸覺反饋
    await AudioService.counterClick();

    // 按鈕動畫
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // 波紋動畫
    rippleAnim.setValue(0);
    Animated.timing(rippleAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();

    // 增加計數
    const newCount = currentCount + 1;
    setCurrentCount(newCount);

    // 檢查是否完成目標
    if (newCount >= target) {
      setTimeout(() => {
        handleComplete();
      }, 500);
    }
  };

  const handleComplete = async () => {
    await AudioService.counterComplete();
    
    Alert.alert(
      '🎉 修行完成！',
      `恭喜您完成了本次修行會期\n持誦次數：${currentCount}\n用時：${formatDuration(duration)}`,
      [
        {
          text: '繼續修行',
          onPress: () => {
            // 繼續計數
          },
        },
        {
          text: '迴向功德',
          style: 'default',
          onPress: () => {
            proceedToMerit();
          },
        },
      ]
    );
  };

  const proceedToMerit = async () => {
    // 保存會期記錄
    await saveSession();
    
    // 導航到迴向頁面
    navigation.replace('Merit', {
      sessionData: {
        projectId,
        projectTitle,
        count: currentCount,
        target,
        duration,
        completedAt: new Date().toISOString(),
      },
    });
  };

  const saveSession = async () => {
    try {
      const session = {
        projectId,
        count: currentCount,
        target,
        duration,
        startTime: startTime.toISOString(),
        endTime: new Date().toISOString(),
        completed: currentCount >= target,
      };

      await StorageService.addSession(session);

      // 更新項目總計數
      if (project) {
        const newTotalCount = (project.currentTotalCount || 0) + currentCount;
        await StorageService.updateProject(projectId, {
          currentTotalCount: newTotalCount,
        });
      }
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  };

  const handleExit = () => {
    if (currentCount > 0) {
      Alert.alert(
        '確認退出',
        '您有未保存的計數，確定要退出嗎？',
        [
          { text: '取消', style: 'cancel' },
          {
            text: '保存並退出',
            onPress: async () => {
              await saveSession();
              navigation.goBack();
            },
          },
          {
            text: '直接退出',
            style: 'destructive',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const handlePause = () => {
    setIsPaused(!isPaused);
    AudioService.buttonPress();
  };

  const handleReset = () => {
    if (currentCount === 0) return;

    Alert.alert(
      '重置計數',
      '確定要重置本次計數嗎？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '重置',
          style: 'destructive',
          onPress: () => {
            setCurrentCount(0);
            AudioService.buttonPress();
          },
        },
      ]
    );
  };

  const addTen = () => {
    if (isPaused) return;
    
    const newCount = Math.min(currentCount + 10, target);
    setCurrentCount(newCount);
    AudioService.buttonPress();

    if (newCount >= target) {
      setTimeout(() => {
        handleComplete();
      }, 500);
    }
  };

  const subtractOne = () => {
    if (isPaused || currentCount === 0) return;
    
    setCurrentCount(prev => Math.max(prev - 1, 0));
    AudioService.buttonPress();
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  };

  const getAverageSpeed = () => {
    if (currentCount === 0 || duration === 0) return 0;
    return Math.round((currentCount / duration) * 60);
  };

  const getTotalProgress = () => {
    if (!project || !totalStats) return '-- / --';
    return `${totalStats.totalCount.toLocaleString()} / ${project.totalGoalCount.toLocaleString()}`;
  };

  const getTotalPercentage = () => {
    if (!project || !totalStats) return 0;
    return project.totalGoalCount > 0 
      ? ((totalStats.totalCount / project.totalGoalCount) * 100).toFixed(3)
      : 0;
  };

  // 進度環參數
  const circumference = 2 * Math.PI * (RING_SIZE / 2 - 20);
  const strokeDashoffset = circumference - (progressAnim as any)._value * circumference;

  return (
    <LinearGradient
      colors={[Colors.backgroundSecondary, Colors.background]}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        {/* 頂部信息 */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleExit}>
            <Ionicons name="arrow-back" size={24} color={Colors.textSecondary} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <Text style={styles.projectTitle} numberOfLines={1}>
              {projectTitle}
            </Text>
            <View style={styles.progressInfo}>
              <Text style={styles.totalProgress}>{getTotalProgress()}</Text>
              <Text style={styles.totalPercentage}>{getTotalPercentage()}%</Text>
            </View>
          </View>
          
          <TouchableOpacity style={styles.pauseButton} onPress={handlePause}>
            <Ionicons 
              name={isPaused ? "play" : "pause"} 
              size={24} 
              color={Colors.textSecondary} 
            />
          </TouchableOpacity>
        </View>

        {/* 主計數區域 */}
        <View style={styles.counterSection}>
          {/* 進度環 */}
          <View style={styles.progressRing}>
            <Svg width={RING_SIZE} height={RING_SIZE}>
              {/* 背景環 */}
              <Circle
                cx={RING_SIZE / 2}
                cy={RING_SIZE / 2}
                r={RING_SIZE / 2 - 20}
                stroke={Colors.backgroundTertiary}
                strokeWidth={12}
                fill="none"
              />
              {/* 進度環 */}
              <Animated.View>
                <Circle
                  cx={RING_SIZE / 2}
                  cy={RING_SIZE / 2}
                  r={RING_SIZE / 2 - 20}
                  stroke={Colors.primary}
                  strokeWidth={12}
                  fill="none"
                  strokeLinecap="round"
                  strokeDasharray={circumference}
                  strokeDashoffset={strokeDashoffset}
                  transform={`rotate(-90 ${RING_SIZE / 2} ${RING_SIZE / 2})`}
                />
              </Animated.View>
            </Svg>
            
            {/* 中心內容 */}
            <View style={styles.ringContent}>
              <Text style={styles.currentCount}>{currentCount}</Text>
              <Text style={styles.targetCount}>/ {target}</Text>
            </View>
          </View>

          {/* 計數按鈕 */}
          <Animated.View style={[styles.counterButton, { transform: [{ scale: scaleAnim }] }]}>
            <TouchableOpacity
              style={styles.counterTouchable}
              onPress={handleCounterPress}
              disabled={isPaused}
              activeOpacity={0.9}
            >
              <LinearGradient
                colors={isPaused ? [Colors.gray300, Colors.gray400] : Colors.gradientPrimary}
                style={styles.counterGradient}
              >
                <Ionicons
                  name="checkmark"
                  size={48}
                  color={Colors.white}
                />
                <Text style={styles.counterText}>
                  {isPaused ? '已暫停' : '點擊持誦'}
                </Text>
              </LinearGradient>
              
              {/* 波紋效果 */}
              <Animated.View
                style={[
                  styles.ripple,
                  {
                    transform: [
                      {
                        scale: rippleAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0, 2],
                        }),
                      },
                    ],
                    opacity: rippleAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.3, 0],
                    }),
                  },
                ]}
              />
            </TouchableOpacity>
          </Animated.View>

          {/* 快速操作 */}
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.quickButton}
              onPress={addTen}
              disabled={isPaused}
            >
              <Text style={styles.quickButtonText}>+10</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickButton, currentCount === 0 && styles.quickButtonDisabled]}
              onPress={subtractOne}
              disabled={isPaused || currentCount === 0}
            >
              <Text style={[styles.quickButtonText, currentCount === 0 && styles.quickButtonTextDisabled]}>
                -1
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.quickButton}
              onPress={handleReset}
              disabled={isPaused}
            >
              <Ionicons name="refresh" size={20} color={Colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* 底部統計 */}
        <View style={styles.statsSection}>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>開始時間</Text>
            <Text style={styles.statValue}>
              {startTime.toLocaleTimeString('zh-TW', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>持續時間</Text>
            <Text style={styles.statValue}>{formatDuration(duration)}</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>平均速度</Text>
            <Text style={styles.statValue}>{getAverageSpeed()} /分</Text>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
    backgroundColor: Colors.white,
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  projectTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  progressInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  totalProgress: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  totalPercentage: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary,
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  pauseButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
    backgroundColor: Colors.white,
  },
  counterSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  progressRing: {
    position: 'relative',
    marginBottom: 40,
  },
  ringContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  currentCount: {
    fontSize: 48,
    fontWeight: '700',
    color: Colors.textPrimary,
    lineHeight: 56,
  },
  targetCount: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  counterButton: {
    width: COUNTER_SIZE,
    height: COUNTER_SIZE,
    marginBottom: 40,
  },
  counterTouchable: {
    width: '100%',
    height: '100%',
    borderRadius: COUNTER_SIZE / 2,
    overflow: 'hidden',
    position: 'relative',
  },
  counterGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  counterText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
  },
  ripple: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: COUNTER_SIZE,
    height: COUNTER_SIZE,
    borderRadius: COUNTER_SIZE / 2,
    backgroundColor: Colors.white,
    marginTop: -COUNTER_SIZE / 2,
    marginLeft: -COUNTER_SIZE / 2,
  },
  quickActions: {
    flexDirection: 'row',
    gap: 20,
  },
  quickButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.border,
  },
  quickButtonDisabled: {
    opacity: 0.5,
  },
  quickButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  quickButtonTextDisabled: {
    color: Colors.textTertiary,
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: Colors.textTertiary,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
});

export default CounterScreen;
