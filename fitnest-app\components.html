<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>組件展示 - Fitnest</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/activity.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .component-section {
            margin-bottom: var(--spacing-2xl);
            padding: var(--spacing-lg);
            background: var(--gray-50);
            border-radius: var(--radius-lg);
        }
        .component-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-500);
            margin-bottom: var(--spacing-lg);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: var(--spacing-sm);
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 頂部導航 -->
        <header class="header">
            <div class="header-content">
                <button class="back-btn" onclick="window.location.href='index.html'">
                    <svg viewBox="0 0 24 24">
                        <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                    </svg>
                </button>
                <h1 class="page-title">組件展示</h1>
                <div></div>
            </div>
        </header>

        <!-- 主要內容 -->
        <main class="main-content">
            <!-- 按鈕組件 -->
            <section class="component-section">
                <h2 class="component-title">按鈕組件</h2>
                <div class="button-grid">
                    <button class="workout-action-btn">
                        <svg viewBox="0 0 24 24">
                            <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                        </svg>
                    </button>
                    <button class="icon-btn">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z"/>
                        </svg>
                    </button>
                    <button class="see-more-btn">查看更多</button>
                    <button class="chart-btn active">週</button>
                    <button class="chart-btn">月</button>
                    <button class="filter-btn">
                        <svg viewBox="0 0 24 24">
                            <path d="M6,13H18V11H6M3,6V8H21V6M10,18H14V16H10V18Z"/>
                        </svg>
                    </button>
                </div>
            </section>

            <!-- 狀態卡片 -->
            <section class="component-section">
                <h2 class="component-title">狀態卡片</h2>
                <div class="status-grid">
                    <div class="status-card heart-rate">
                        <div class="card-header">
                            <span class="card-title">心率</span>
                            <svg class="card-icon" viewBox="0 0 24 24">
                                <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.04L12,21.35Z"/>
                            </svg>
                        </div>
                        <div class="card-value">78 <span class="unit">BPM</span></div>
                        <div class="card-chart heart-chart"></div>
                    </div>

                    <div class="status-card water-intake">
                        <div class="card-header">
                            <span class="card-title">水分攝取</span>
                            <svg class="card-icon" viewBox="0 0 24 24">
                                <path d="M12,2A1,1 0 0,1 13,3A1,1 0 0,1 12,4A1,1 0 0,1 11,3A1,1 0 0,1 12,2M21,9V7L19,5.5V4.5A1.5,1.5 0 0,0 17.5,3A1.5,1.5 0 0,0 16,4.5V5.5L14,7V9A1,1 0 0,0 15,10V11L17,13H19V11A1,1 0 0,0 20,10V9M3,9V7L1,5.5V4.5A1.5,1.5 0 0,1 2.5,3A1.5,1.5 0 0,1 4,4.5V5.5L6,7V9A1,1 0 0,1 5,10V11L3,13H1V11A1,1 0 0,1 2,10V9"/>
                            </svg>
                        </div>
                        <div class="card-value">2.5 <span class="unit">L</span></div>
                        <div class="water-progress">
                            <div class="water-fill" style="--fill: 62.5%"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 摘要卡片 -->
            <section class="component-section">
                <h2 class="component-title">摘要卡片</h2>
                <div class="summary-cards">
                    <div class="summary-card steps">
                        <div class="card-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M14,12L10,8V11H2V13H10V16L14,12Z"/>
                            </svg>
                        </div>
                        <div class="card-info">
                            <span class="card-value">8,547</span>
                            <span class="card-label">步數</span>
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="--progress: 85.47%"></div>
                        </div>
                    </div>

                    <div class="summary-card calories">
                        <div class="card-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M11.71,19C9.93,19 8.5,17.59 8.5,15.86C8.5,14.24 9.53,13.1 11.3,12.74C13.07,12.38 14.9,11.53 15.92,10.16C16.31,11.45 16.5,12.81 16.5,14.2C16.5,16.84 14.36,19 11.71,19M13.5,0.67C13.5,0.67 14.24,3.32 14.24,5.47C14.24,7.53 12.89,9.2 10.83,9.2C8.76,9.2 7.2,7.53 7.2,5.47L7.23,5.1C2.52,7.14 2.52,16.11 7.23,18.15C8.14,18.56 9.18,18.78 10.28,18.78C15.28,18.78 19.12,14.94 19.12,9.94C19.12,5.25 15.81,1.34 13.5,0.67Z"/>
                            </svg>
                        </div>
                        <div class="card-info">
                            <span class="card-value">423</span>
                            <span class="card-label">卡路里</span>
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="--progress: 70.5%"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 進度環 -->
            <section class="component-section">
                <h2 class="component-title">進度環</h2>
                <div class="component-grid">
                    <div class="target-card">
                        <div class="target-info">
                            <span class="target-label">活動卡路里</span>
                            <span class="target-value">760 / 900</span>
                        </div>
                        <div class="progress-ring">
                            <svg class="progress-svg" viewBox="0 0 100 100">
                                <defs>
                                    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                                        <stop offset="0%" style="stop-color:#92A3FD;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#9DCEFF;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <circle cx="50" cy="50" r="45" class="progress-bg"/>
                                <circle cx="50" cy="50" r="45" class="progress-fill" style="--progress: 84.4%; stroke: url(#gradient2)"/>
                            </svg>
                            <span class="progress-text">84%</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 運動卡片 -->
            <section class="component-section">
                <h2 class="component-title">運動卡片</h2>
                <div class="workout-cards">
                    <div class="workout-card active">
                        <div class="workout-image fullbody-bg"></div>
                        <div class="workout-info">
                            <h4>全身運動</h4>
                            <p>11 個練習 | 32 分鐘</p>
                        </div>
                        <button class="workout-action-btn">
                            <svg viewBox="0 0 24 24">
                                <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                            </svg>
                        </button>
                    </div>

                    <div class="workout-card">
                        <div class="workout-image lowerbody-bg"></div>
                        <div class="workout-info">
                            <h4>下半身運動</h4>
                            <p>12 個練習 | 40 分鐘</p>
                        </div>
                        <button class="workout-action-btn">
                            <svg viewBox="0 0 24 24">
                                <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 圖表組件 -->
            <section class="component-section">
                <h2 class="component-title">圖表組件</h2>
                <div class="chart-container">
                    <div class="chart-bars">
                        <div class="chart-bar" style="--height: 60%">
                            <span class="bar-value">6k</span>
                            <div class="bar-fill"></div>
                            <span class="bar-label">週一</span>
                        </div>
                        <div class="chart-bar" style="--height: 80%">
                            <span class="bar-value">8k</span>
                            <div class="bar-fill"></div>
                            <span class="bar-label">週二</span>
                        </div>
                        <div class="chart-bar active" style="--height: 95%">
                            <span class="bar-value">9.5k</span>
                            <div class="bar-fill"></div>
                            <span class="bar-label">今天</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 歷史項目 -->
            <section class="component-section">
                <h2 class="component-title">歷史項目</h2>
                <div class="history-list">
                    <div class="history-item">
                        <div class="item-icon workout">
                            <svg viewBox="0 0 24 24">
                                <path d="M20.57,14.86L22,13.43L20.57,12L17,15.57L8.43,7L12,3.43L10.57,2L9.14,3.43L7.71,2L5.57,4.14L4.14,2.71L2.71,4.14L4.14,5.57L2,7.71L3.43,9.14L2,10.57L3.43,12L7,8.43L15.57,17L12,20.57L13.43,22L14.86,20.57L16.29,22L18.43,19.86L19.86,21.29L21.29,19.86L19.86,18.43L22,16.29L20.57,14.86Z"/>
                            </svg>
                        </div>
                        <div class="item-info">
                            <h4>全身運動</h4>
                            <p>32 分鐘 • 180 卡路里</p>
                            <span class="item-time">2 小時前</span>
                        </div>
                        <div class="item-status completed">
                            <svg viewBox="0 0 24 24">
                                <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 測試按鈕 -->
            <section class="component-section">
                <h2 class="component-title">交互測試</h2>
                <div class="button-grid">
                    <button onclick="showTestModal()" class="see-more-btn">顯示模態框</button>
                    <button onclick="showTestToast()" class="see-more-btn">顯示提示</button>
                    <button onclick="animateProgress()" class="see-more-btn">動畫進度</button>
                </div>
            </section>
        </main>

        <!-- 底部導航 -->
        <nav class="bottom-nav">
            <button class="nav-item" onclick="window.location.href='index.html'">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"/>
                </svg>
                <span class="nav-label">首頁</span>
            </button>
            <button class="nav-item" onclick="window.location.href='activity.html'">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M9,10V12H7V10H9M13,10V12H11V10H13M17,10V12H15V10H17M19,3A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5A2,2 0 0,1 5,3H6V1H8V3H16V1H18V3H19M19,19V8H5V19H19M9,14V16H7V14H9M13,14V16H11V14H13M17,14V16H15V14H17Z"/>
                </svg>
                <span class="nav-label">活動</span>
            </button>
            <button class="nav-item active">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z"/>
                </svg>
                <span class="nav-label">組件</span>
            </button>
            <button class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9A3,3 0 0,0 9,12M12,4.5C17,4.5 21.27,7.61 23,12C21.27,16.39 17,19.5 12,19.5C7,19.5 2.73,16.39 1,12C2.73,7.61 7,4.5 12,4.5M3.18,12C4.83,15.36 8.24,17.5 12,17.5C15.76,17.5 19.17,15.36 20.82,12C19.17,8.64 15.76,6.5 12,6.5C8.24,6.5 4.83,8.64 3.18,12Z"/>
                </svg>
                <span class="nav-label">相機</span>
            </button>
            <button class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                </svg>
                <span class="nav-label">個人資料</span>
            </button>
        </nav>
    </div>

    <script src="js/app.js"></script>
    <script>
        function showTestModal() {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <h3>測試模態框</h3>
                    <p>這是一個測試模態框，展示模態框組件的效果。</p>
                    <button class="modal-btn" onclick="this.closest('.modal-overlay').remove()">關閉</button>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function showTestToast() {
            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.textContent = '這是一個測試提示框！';
            document.body.appendChild(toast);
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        function animateProgress() {
            const progressBars = document.querySelectorAll('.progress-bar');
            const progressFills = document.querySelectorAll('.progress-fill');
            const waterFills = document.querySelectorAll('.water-fill');
            
            progressBars.forEach(bar => {
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = bar.style.getPropertyValue('--progress') || '50%';
                }, 100);
            });
            
            progressFills.forEach(fill => {
                fill.style.strokeDashoffset = '283';
                setTimeout(() => {
                    const progress = fill.style.getPropertyValue('--progress') || '50%';
                    fill.style.strokeDashoffset = `calc(283 - (283 * ${progress}) / 100)`;
                }, 100);
            });
            
            waterFills.forEach(fill => {
                fill.style.width = '0%';
                setTimeout(() => {
                    fill.style.width = fill.style.getPropertyValue('--fill') || '50%';
                }, 100);
            });
        }

        // 頁面載入時執行動畫
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(animateProgress, 500);
        });
    </script>
</body>
</html>
