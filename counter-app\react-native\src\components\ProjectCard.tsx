import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Project, ProjectStats } from '../types';
import { StorageService } from '../services/StorageService';

interface ProjectCardProps {
  project: Project;
  onPress: () => void;
  onLongPress?: () => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onPress,
  onLongPress,
}) => {
  const [stats, setStats] = useState<ProjectStats>({
    sessionCount: 0,
    totalCount: 0,
    totalDuration: 0,
    avgSessionCount: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const scaleAnim = new Animated.Value(1);

  useEffect(() => {
    loadStats();
  }, [project.id]);

  const loadStats = async () => {
    try {
      const projectStats = await StorageService.getProjectStats(project.id);
      setStats(projectStats);
    } catch (error) {
      console.error('Failed to load project stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const getProgressPercentage = () => {
    if (project.totalGoalCount === 0) return 0;
    return Math.min((stats.totalCount / project.totalGoalCount) * 100, 100);
  };

  const getProgressColor = () => {
    const percentage = getProgressPercentage();
    if (percentage >= 100) return Colors.success;
    if (percentage >= 75) return Colors.primary;
    if (percentage >= 50) return Colors.warning;
    return Colors.accent;
  };

  return (
    <Animated.View style={[styles.container, { transform: [{ scale: scaleAnim }] }]}>
      <TouchableOpacity
        style={styles.card}
        onPress={onPress}
        onLongPress={onLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        {/* 卡片背景 */}
        <View style={styles.cardBackground}>
          {/* 頂部信息 */}
          <View style={styles.header}>
            <View style={styles.projectInfo}>
              <Text style={styles.title} numberOfLines={1}>
                {project.title}
              </Text>
              {project.description && (
                <Text style={styles.description} numberOfLines={2}>
                  {project.description}
                </Text>
              )}
            </View>
            
            <View style={styles.iconContainer}>
              <LinearGradient
                colors={Colors.gradientSecondary}
                style={styles.iconGradient}
              >
                <Ionicons
                  name="flower-outline"
                  size={24}
                  color={Colors.white}
                />
              </LinearGradient>
            </View>
          </View>

          {/* 進度信息 */}
          <View style={styles.progressSection}>
            <View style={styles.progressText}>
              <Text style={styles.progressCurrent}>
                {stats.totalCount.toLocaleString()} / {project.totalGoalCount.toLocaleString()}
              </Text>
              <Text style={[styles.progressPercentage, { color: getProgressColor() }]}>
                {getProgressPercentage().toFixed(1)}%
              </Text>
            </View>
            
            {/* 進度條 */}
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${getProgressPercentage()}%`,
                    backgroundColor: getProgressColor(),
                  },
                ]}
              />
            </View>
          </View>

          {/* 統計信息 */}
          <View style={styles.statsSection}>
            <View style={styles.statItem}>
              <Ionicons
                name="time-outline"
                size={16}
                color={Colors.textSecondary}
              />
              <Text style={styles.statText}>
                {stats.sessionCount} 次會期
              </Text>
            </View>
            
            {stats.lastSessionAt && (
              <View style={styles.statItem}>
                <Ionicons
                  name="calendar-outline"
                  size={16}
                  color={Colors.textSecondary}
                />
                <Text style={styles.statText}>
                  {StorageService.formatDate(stats.lastSessionAt)}
                </Text>
              </View>
            )}
          </View>

          {/* 完成標記 */}
          {getProgressPercentage() >= 100 && (
            <View style={styles.completedBadge}>
              <Ionicons
                name="checkmark-circle"
                size={20}
                color={Colors.success}
              />
              <Text style={styles.completedText}>已完成</Text>
            </View>
          )}
        </View>

        {/* 卡片邊框效果 */}
        <LinearGradient
          colors={[Colors.primaryLight, Colors.secondaryLight]}
          style={styles.borderGradient}
        />
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 4,
  },
  card: {
    position: 'relative',
    borderRadius: 16,
    overflow: 'hidden',
  },
  cardBackground: {
    backgroundColor: Colors.white,
    padding: 20,
    borderRadius: 16,
  },
  borderGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
    padding: 2,
    zIndex: -1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  projectInfo: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  description: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textSecondary,
    lineHeight: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
  },
  iconGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressCurrent: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  progressPercentage: {
    fontSize: 12,
    fontWeight: '600',
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.backgroundTertiary,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textSecondary,
  },
  completedBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.successLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  completedText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.success,
  },
});

export default ProjectCard;
