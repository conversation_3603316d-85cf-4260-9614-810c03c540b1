/* 活動頁面專用樣式 */

/* 頁面標題和返回按鈕 */
.back-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-50);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.back-btn svg {
    width: 20px;
    height: 20px;
    fill: var(--gray-400);
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-500);
}

/* 活動摘要 */
.activity-summary h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-lg);
}

.summary-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.summary-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.summary-card .card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.summary-card .card-icon svg {
    width: 20px;
    height: 20px;
    fill: var(--white);
}

.summary-card.steps .card-icon {
    background: var(--gradient-primary);
}

.summary-card.distance .card-icon {
    background: var(--gradient-secondary);
}

.summary-card.calories .card-icon {
    background: linear-gradient(315deg, var(--warning-color) 0%, #FFB800 100%);
}

.summary-card.time .card-icon {
    background: linear-gradient(315deg, var(--success-color) 0%, #7ED321 100%);
}

.summary-card .card-info {
    margin-bottom: var(--spacing-md);
}

.summary-card .card-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-500);
    margin-bottom: var(--spacing-xs);
}

.summary-card .card-label {
    font-size: 12px;
    color: var(--gray-400);
    font-weight: 500;
}

.summary-card .card-progress {
    height: 4px;
    background: var(--gray-100);
    border-radius: 2px;
    overflow: hidden;
}

.summary-card .progress-bar {
    height: 100%;
    width: var(--progress);
    border-radius: 2px;
    transition: width 0.8s ease;
}

.summary-card.steps .progress-bar {
    background: var(--gradient-primary);
}

.summary-card.distance .progress-bar {
    background: var(--gradient-secondary);
}

.summary-card.calories .progress-bar {
    background: linear-gradient(90deg, var(--warning-color) 0%, #FFB800 100%);
}

.summary-card.time .progress-bar {
    background: linear-gradient(90deg, var(--success-color) 0%, #7ED321 100%);
}

/* 週活動圖表 */
.weekly-chart {
    margin-bottom: var(--spacing-xl);
}

.weekly-chart .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.chart-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    border: none;
    background: var(--gray-100);
    color: var(--gray-400);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-btn.active {
    background: var(--gradient-primary);
    color: var(--white);
}

.chart-container {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.chart-bars {
    display: flex;
    justify-content: space-between;
    align-items: end;
    height: 200px;
    gap: var(--spacing-sm);
}

.chart-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    position: relative;
}

.bar-value {
    font-size: 10px;
    color: var(--gray-400);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.chart-bar:hover .bar-value,
.chart-bar.active .bar-value {
    opacity: 1;
}

.bar-fill {
    width: 100%;
    max-width: 24px;
    height: var(--height);
    background: var(--gray-100);
    border-radius: 12px;
    margin-bottom: var(--spacing-sm);
    position: relative;
    transition: all 0.3s ease;
}

.chart-bar.active .bar-fill {
    background: var(--gradient-primary);
}

.chart-bar:hover .bar-fill {
    background: var(--gradient-secondary);
    transform: scaleY(1.05);
}

.bar-label {
    font-size: 10px;
    color: var(--gray-400);
    font-weight: 500;
}

.chart-bar.active .bar-label {
    color: var(--primary-color);
    font-weight: 600;
}

/* 運動歷史 */
.workout-history .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.filter-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--gray-100);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background: var(--gray-200);
}

.filter-btn svg {
    width: 16px;
    height: 16px;
    fill: var(--gray-400);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.history-item {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.2s ease;
}

.history-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.item-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.item-icon svg {
    width: 24px;
    height: 24px;
    fill: var(--white);
}

.item-icon.workout {
    background: var(--gradient-primary);
}

.item-icon.cardio {
    background: linear-gradient(315deg, var(--error-color) 0%, #FF8A80 100%);
}

.item-icon.strength {
    background: linear-gradient(315deg, var(--accent-color) 0%, #EEA4CE 100%);
}

.item-info {
    flex: 1;
}

.item-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-500);
    margin-bottom: var(--spacing-xs);
}

.item-info p {
    font-size: 12px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-xs);
}

.item-time {
    font-size: 10px;
    color: var(--gray-300);
    font-weight: 500;
}

.item-status {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.item-status.completed {
    background: var(--success-color);
}

.item-status svg {
    width: 16px;
    height: 16px;
    fill: var(--white);
}

/* 動畫效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.summary-card,
.chart-container,
.history-item {
    animation: slideInUp 0.6s ease forwards;
}

.summary-card:nth-child(1) { animation-delay: 0.1s; }
.summary-card:nth-child(2) { animation-delay: 0.2s; }
.summary-card:nth-child(3) { animation-delay: 0.3s; }
.summary-card:nth-child(4) { animation-delay: 0.4s; }

.chart-container { animation-delay: 0.5s; }

.history-item:nth-child(1) { animation-delay: 0.6s; }
.history-item:nth-child(2) { animation-delay: 0.7s; }
.history-item:nth-child(3) { animation-delay: 0.8s; }

/* 篩選模態框 */
.filter-modal {
    max-width: 280px;
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.filter-option {
    padding: var(--spacing-md);
    border: 1px solid var(--gray-200);
    background: var(--white);
    border-radius: var(--radius-md);
    font-size: 14px;
    color: var(--gray-500);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.filter-option:hover {
    border-color: var(--primary-color);
    background: var(--gradient-card);
}

.filter-option.active {
    border-color: var(--primary-color);
    background: var(--gradient-primary);
    color: var(--white);
}

.close-btn {
    width: 100%;
    background: var(--gray-100);
    color: var(--gray-400);
}

/* 響應式設計 */
@media (max-width: 320px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }

    .chart-bars {
        gap: var(--spacing-xs);
    }

    .bar-fill {
        max-width: 20px;
    }

    .filter-modal {
        max-width: 260px;
    }
}

@media (min-width: 768px) {
    .summary-cards {
        grid-template-columns: repeat(4, 1fr);
    }

    .chart-bars {
        height: 240px;
    }

    .bar-fill {
        max-width: 32px;
    }

    .filter-modal {
        max-width: 320px;
    }
}
