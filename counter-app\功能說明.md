咒語計數器手動版
eact native技術實現, 所有資料以本地存儲來實現, 
ui使用fitnest-app的元件及風格樣式

### **第一部分：App 核心概念與 MVP 範疇**

1.  **App 定位 (MVP 版)：**
    *   **核心功能：**一個專注、無干擾的「手動修行計數器」。
    *   **價值主張：**幫助用戶設定長期目標，輕鬆完成每日的計數任務，並將功德透過數位化方式進行迴向與分享。
    *   **技術範疇：**
        *   **計數模式：**僅手動點擊模式 (Manual Mode Only)。
        *   **數據儲存：**所有數據 100% 儲存在使用者手機本機 (Local Storage Only)。無需網路，無需註冊。

---

### **第二部分：畫面與使用者介面 (UI/UX) 設計**

這部分將嚴格按照您的描述來規劃。

1.  **主畫面 (Home Screen)：修行項目列表**
    *   **功能：**展示所有已創建的「修行項目」。
    *   **列表項目顯示：**
        *   **標題：**例如「六字大明咒」。
        *   **進度條/文字：**清晰顯示「總進度」，例如 `520 / 1,000,000`。
    *   **互動：**
        *   右上角或右下角有一個「+」按鈕，用於創建新的修行項目。
        *   點擊任何一個項目，即開始「開始一個新會期」的流程。

2.  **計數畫面 (Counter Screen)：**
    *   **佈局設計：**極簡、專注，避免任何不必要的干擾。
    *   **計數資訊顯示 (完全按照您的三行要求)：**
        *   **第一行 (頂部)：** `[項目標題]` (例如：「六字大明咒」)
        *   **第二行 (中間)：** `[目前總累計數] / [項目總目標數]` (例如：`520 / 1,000,000`)
        *   **第三行 (視覺中心)：** **巨大、醒目的字體**顯示`[本次計數次數]` (例如：`25`)
    *   **核心互動元件：**
        *   一個**巨大的點擊區域**（例如，螢幕的下半部分整個都是按鈕），用戶每點擊一次，第三行的「本次計數次數」就 +1。

3.  **願望清單畫面 (Wish List Screen)：**
    *   **功能：**一個獨立的頁面，可透過主畫面的選單或分頁按鈕進入。
    *   **介面：**一個簡單的列表，用戶可以新增、編輯、刪除願望文字。
    *   **視覺標記：**當一個願望被「灌注」後，可以在其旁邊顯示一個小圖標（如愛心、光芒）或更新日期，給予用戶正向回饋。

---

### **第三部分：核心使用者流程 (User Flow)**

這是將所有畫面串連起來的劇本。

1.  **流程一：創建一個新的修行項目**
    1.  在主畫面，用戶點擊「+」按鈕。
    2.  彈出一個設定視窗或跳轉到新頁面。
    3.  用戶輸入**「自訂標題」**（例如：為家人祈福）。
    4.  用戶輸入**「項目總目標數」**（例如：100000）。
    5.  點擊「儲存」。App 返回主畫面，新的項目出現在列表中，進度為 `0 / 100000`。

2.  **流程二：執行一次計數會期 (Session)**
    1.  在主畫面，用戶點擊「六字大明咒」這個項目。
    2.  **立即彈出一個輸入視窗**，提示用戶：「請設定本次需提醒的達成次數」。(Requirement 3)
    3.  用戶輸入 `108`，然後點擊「開始」。
    4.  App 進入「計數畫面」。畫面顯示：
        *   六字大明咒
        *   520 / 1,000,000
        *   **0** (大字體)
    5.  用戶開始點擊大按鈕，大數字從 0 變為 1, 2, 3 ... 107。
    6.  當用戶點擊第 108 次時：
        *   大數字跳到 **108**。
        *   **立即觸發**：系統發出清脆的「完成音效」並伴隨手機「震動」。
        *   **背景自動運算：**App 將「項目總累計數」更新為 `520 + 108 = 628`。
        *   **自動跳轉：**App 自動離開計數畫面，進入「迴向」流程。

3.  **流程三：迴向、分享與灌注願望**
    1.  App 進入一個新的「完成與迴向」頁面。
    2.  頁面上有一個文字編輯區，並已預設填好文案：
        > `我剛剛完成了「六字大明咒」的修行，本次持誦 108 次，總計已達 628 次。`
        > `[游標在此，鼓勵用戶輸入自訂的祝福與迴向文...]`
    3.  在文案下方，有兩個按鈕：
        *   **[灌注到願望清單]：**
            *   點擊後，彈出用戶的「願望清單」列表。
            *   用戶可以勾選一個或多個願望。
            *   點擊「確認」後，彈窗關閉，被選中的願望上會出現已祝福的標記。
            *  在願望清單列表中也有祝褔記錄列表, 顯示日期時間,灌注的行程標題及持誦數
        *   **[分享到社群]：**
            *   點擊後，調用手機系統的分享功能。
            *   將文字區內的所有內容（包含預設文案和用戶自訂內容）作為純文字分享出去。
    4.  用戶完成操作後，可以點擊「完成」或「返回」按鈕，回到 App 的主畫面。此時主畫面上該項目的進度已更新為 `628 / 1,000,000`。

---

### **第四部分：數據結構設計 (給開發者的藍圖)**

即使不寫程式碼，定義好數據結構也至關重要。

1.  **「修行項目」數據模型 (Project Model):**
    *   `project_id`: 唯一ID (例如：一串隨機數字)
    *   `title`: 標題 (文字)
    *   `total_goal_count`: 總目標數 (整數)
    *   `current_total_count`: 當前總累計數 (整數)

2.  **「願望」數據模型 (Wish Model):**
    *   `wish_id`: 唯一ID
    *   `wish_text`: 願望內容 (文字)
    *   `creation_date`: 創建日期 (日期時間格式)
    *   `last_infused_date`: 最近一次被灌注的日期 (日期時間格式，可選)

