// 本地存儲管理模塊

class StorageManager {
    constructor() {
        this.keys = {
            projects: 'mantra_projects',
            wishes: 'mantra_wishes',
            sessions: 'mantra_sessions',
            blessings: 'mantra_blessings',
            settings: 'mantra_settings'
        };
        this.initializeStorage();
    }

    // 初始化存儲
    initializeStorage() {
        if (!this.getProjects()) {
            this.saveProjects([]);
        }
        if (!this.getWishes()) {
            this.saveWishes([]);
        }
        if (!this.getSessions()) {
            this.saveSessions([]);
        }
        if (!this.getBlessings()) {
            this.saveBlessings([]);
        }
        if (!this.getSettings()) {
            this.saveSettings(this.getDefaultSettings());
        }
    }

    // 獲取默認設置
    getDefaultSettings() {
        return {
            soundEnabled: true,
            vibrationEnabled: true,
            theme: 'light',
            language: 'zh-TW'
        };
    }

    // 通用存儲方法
    save(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('存儲失敗:', error);
            return false;
        }
    }

    // 通用讀取方法
    load(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('讀取失敗:', error);
            return null;
        }
    }

    // 修行項目管理
    getProjects() {
        return this.load(this.keys.projects) || [];
    }

    saveProjects(projects) {
        return this.save(this.keys.projects, projects);
    }

    addProject(project) {
        const projects = this.getProjects();
        project.id = this.generateId();
        project.createdAt = new Date().toISOString();
        project.updatedAt = new Date().toISOString();
        projects.push(project);
        return this.saveProjects(projects);
    }

    updateProject(projectId, updates) {
        const projects = this.getProjects();
        const index = projects.findIndex(p => p.id === projectId);
        if (index !== -1) {
            projects[index] = { ...projects[index], ...updates, updatedAt: new Date().toISOString() };
            return this.saveProjects(projects);
        }
        return false;
    }

    deleteProject(projectId) {
        const projects = this.getProjects();
        const filteredProjects = projects.filter(p => p.id !== projectId);
        return this.saveProjects(filteredProjects);
    }

    getProject(projectId) {
        const projects = this.getProjects();
        return projects.find(p => p.id === projectId);
    }

    // 願望管理
    getWishes() {
        return this.load(this.keys.wishes) || [];
    }

    saveWishes(wishes) {
        return this.save(this.keys.wishes, wishes);
    }

    addWish(wish) {
        const wishes = this.getWishes();
        wish.id = this.generateId();
        wish.createdAt = new Date().toISOString();
        wish.blessedCount = 0;
        wish.lastBlessedAt = null;
        wishes.push(wish);
        return this.saveWishes(wishes);
    }

    updateWish(wishId, updates) {
        const wishes = this.getWishes();
        const index = wishes.findIndex(w => w.id === wishId);
        if (index !== -1) {
            wishes[index] = { ...wishes[index], ...updates };
            return this.saveWishes(wishes);
        }
        return false;
    }

    deleteWish(wishId) {
        const wishes = this.getWishes();
        const filteredWishes = wishes.filter(w => w.id !== wishId);
        return this.saveWishes(filteredWishes);
    }

    getWish(wishId) {
        const wishes = this.getWishes();
        return wishes.find(w => w.id === wishId);
    }

    // 修行會期管理
    getSessions() {
        return this.load(this.keys.sessions) || [];
    }

    saveSessions(sessions) {
        return this.save(this.keys.sessions, sessions);
    }

    addSession(session) {
        const sessions = this.getSessions();
        session.id = this.generateId();
        session.createdAt = new Date().toISOString();
        sessions.push(session);
        return this.saveSessions(sessions);
    }

    getSessionsByProject(projectId) {
        const sessions = this.getSessions();
        return sessions.filter(s => s.projectId === projectId);
    }

    getRecentSessions(limit = 5) {
        const sessions = this.getSessions();
        return sessions
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, limit);
    }

    // 祝福記錄管理
    getBlessings() {
        return this.load(this.keys.blessings) || [];
    }

    saveBlessings(blessings) {
        return this.save(this.keys.blessings, blessings);
    }

    addBlessing(blessing) {
        const blessings = this.getBlessings();
        blessing.id = this.generateId();
        blessing.createdAt = new Date().toISOString();
        blessings.push(blessing);
        
        // 更新願望的祝福計數
        if (blessing.wishIds && blessing.wishIds.length > 0) {
            blessing.wishIds.forEach(wishId => {
                this.incrementWishBlessedCount(wishId);
            });
        }
        
        return this.saveBlessings(blessings);
    }

    incrementWishBlessedCount(wishId) {
        const wishes = this.getWishes();
        const index = wishes.findIndex(w => w.id === wishId);
        if (index !== -1) {
            wishes[index].blessedCount = (wishes[index].blessedCount || 0) + 1;
            wishes[index].lastBlessedAt = new Date().toISOString();
            this.saveWishes(wishes);
        }
    }

    getBlessingsByWish(wishId) {
        const blessings = this.getBlessings();
        return blessings.filter(b => b.wishIds && b.wishIds.includes(wishId));
    }

    // 設置管理
    getSettings() {
        return this.load(this.keys.settings);
    }

    saveSettings(settings) {
        return this.save(this.keys.settings, settings);
    }

    updateSettings(updates) {
        const settings = this.getSettings();
        const newSettings = { ...settings, ...updates };
        return this.saveSettings(newSettings);
    }

    // 統計數據
    getTodayStats() {
        const today = new Date().toDateString();
        const sessions = this.getSessions();
        const todaySessions = sessions.filter(s => 
            new Date(s.createdAt).toDateString() === today
        );

        return {
            sessionCount: todaySessions.length,
            totalCount: todaySessions.reduce((sum, s) => sum + s.count, 0),
            totalDuration: todaySessions.reduce((sum, s) => sum + (s.duration || 0), 0)
        };
    }

    getProjectStats(projectId) {
        const sessions = this.getSessionsByProject(projectId);
        const totalCount = sessions.reduce((sum, s) => sum + s.count, 0);
        const totalDuration = sessions.reduce((sum, s) => sum + (s.duration || 0), 0);
        const avgSessionCount = sessions.length > 0 ? Math.round(totalCount / sessions.length) : 0;

        return {
            sessionCount: sessions.length,
            totalCount,
            totalDuration,
            avgSessionCount,
            lastSessionAt: sessions.length > 0 ? sessions[sessions.length - 1].createdAt : null
        };
    }

    getAllStats() {
        const projects = this.getProjects();
        const sessions = this.getSessions();
        const wishes = this.getWishes();
        const blessings = this.getBlessings();

        return {
            projectCount: projects.length,
            sessionCount: sessions.length,
            totalCount: sessions.reduce((sum, s) => sum + s.count, 0),
            wishCount: wishes.length,
            blessedWishCount: wishes.filter(w => w.blessedCount > 0).length,
            blessingCount: blessings.length
        };
    }

    // 工具方法
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}小時${minutes}分鐘`;
        } else if (minutes > 0) {
            return `${minutes}分${secs}秒`;
        } else {
            return `${secs}秒`;
        }
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return '今天';
        } else if (diffDays === 2) {
            return '昨天';
        } else if (diffDays <= 7) {
            return `${diffDays - 1}天前`;
        } else {
            return date.toLocaleDateString('zh-TW');
        }
    }

    // 數據導出和導入
    exportData() {
        return {
            projects: this.getProjects(),
            wishes: this.getWishes(),
            sessions: this.getSessions(),
            blessings: this.getBlessings(),
            settings: this.getSettings(),
            exportedAt: new Date().toISOString()
        };
    }

    importData(data) {
        try {
            if (data.projects) this.saveProjects(data.projects);
            if (data.wishes) this.saveWishes(data.wishes);
            if (data.sessions) this.saveSessions(data.sessions);
            if (data.blessings) this.saveBlessings(data.blessings);
            if (data.settings) this.saveSettings(data.settings);
            return true;
        } catch (error) {
            console.error('導入數據失敗:', error);
            return false;
        }
    }

    // 清除所有數據
    clearAllData() {
        Object.values(this.keys).forEach(key => {
            localStorage.removeItem(key);
        });
        this.initializeStorage();
    }
}

// 創建全局存儲管理器實例
window.storage = new StorageManager();
