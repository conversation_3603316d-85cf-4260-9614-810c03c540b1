<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咒語計數器 - 修行助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #92A3FD 0%, #9DCEFF 50%, #C58BF2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            overflow-x: hidden;
        }

        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            position: relative;
        }

        .logo-container {
            margin-bottom: 2rem;
            position: relative;
        }

        .logo {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
            background: linear-gradient(45deg, #fff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: float 3s ease-in-out infinite;
        }

        .logo-icon svg {
            width: 40px;
            height: 40px;
            fill: white;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .subtitle {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .nav-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .nav-card:hover::before {
            left: 100%;
        }

        .nav-card:hover {
            transform: translateY(-8px) scale(1.02);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }

        .nav-card-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }

        .nav-card h3 {
            font-size: 1.4rem;
            margin-bottom: 0.8rem;
            font-weight: 600;
        }

        .nav-card p {
            opacity: 0.8;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .features {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2rem;
            margin-top: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .features h3 {
            margin-bottom: 1.5rem;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            opacity: 0.9;
            padding: 0.5rem;
            border-radius: 10px;
            transition: all 0.2s ease;
        }

        .feature-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .feature-item::before {
            content: "🙏";
            font-size: 1.2rem;
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .tech-stack {
            margin-top: 2rem;
            opacity: 0.8;
        }

        .tech-stack h4 {
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }

        .tech-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.4rem 1rem;
            border-radius: 25px;
            font-size: 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.2s ease;
        }

        .tech-tag:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .footer {
            margin-top: 3rem;
            opacity: 0.7;
            font-size: 0.9rem;
        }

        /* 背景裝飾 */
        .bg-decoration {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float-bg 6s ease-in-out infinite;
        }

        .bg-circle:nth-child(1) {
            width: 200px;
            height: 200px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .bg-circle:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .bg-circle:nth-child(3) {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float-bg {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .logo {
                font-size: 2.5rem;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 背景裝飾 -->
    <div class="bg-decoration">
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
    </div>

    <div class="container">
        <div class="logo-container">
            <div class="logo-icon">
                <svg viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
                </svg>
            </div>
            <h1 class="logo">咒語計數器</h1>
            <p class="subtitle">現代化修行助手 · 功德無量</p>
        </div>
        
        <div class="nav-grid">
            <a href="index.html" class="nav-card">
                <span class="nav-card-icon">🏠</span>
                <h3>開始修行</h3>
                <p>創建修行項目，設定目標，開始您的持誦之旅。支持多種咒語和經文計數。</p>
            </a>
            
            <a href="wishlist.html" class="nav-card">
                <span class="nav-card-icon">💝</span>
                <h3>願望清單</h3>
                <p>管理個人願望，將修行功德灌注到心願中，讓修行更有意義。</p>
            </a>
            
            <a href="counter.html" class="nav-card" onclick="checkSession(event)">
                <span class="nav-card-icon">📿</span>
                <h3>計數界面</h3>
                <p>極簡的計數界面，專注於修行體驗。大按鈕設計，觸摸友好。</p>
            </a>
            
            <a href="merit.html" class="nav-card" onclick="checkCompletion(event)">
                <span class="nav-card-icon">🌟</span>
                <h3>功德迴向</h3>
                <p>完成修行後進行功德迴向，分享修行成果，利益眾生。</p>
            </a>
        </div>

        <div class="features">
            <h3>✨ 應用特色</h3>
            <div class="feature-grid">
                <div class="feature-item">極簡計數界面</div>
                <div class="feature-item">修行進度追蹤</div>
                <div class="feature-item">願望功德灌注</div>
                <div class="feature-item">迴向文編輯</div>
                <div class="feature-item">社群分享功能</div>
                <div class="feature-item">本地數據存儲</div>
                <div class="feature-item">響應式設計</div>
                <div class="feature-item">觸覺音效反饋</div>
            </div>
            
            <div class="tech-stack">
                <h4>🛠️ 技術特色</h4>
                <div class="tech-tags">
                    <span class="tech-tag">現代化 UI</span>
                    <span class="tech-tag">PWA 支持</span>
                    <span class="tech-tag">離線使用</span>
                    <span class="tech-tag">觸摸優化</span>
                    <span class="tech-tag">數據安全</span>
                    <span class="tech-tag">跨平台</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>願此應用能幫助更多修行者精進修行 🙏</p>
            <p>功德無量 · 阿彌陀佛</p>
        </div>
    </div>

    <script>
        // 頁面載入動畫
        document.addEventListener('DOMContentLoaded', () => {
            const container = document.querySelector('.container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 1s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 200);

            // 卡片動畫
            const cards = document.querySelectorAll('.nav-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 500 + index * 200);
            });
        });

        // 檢查會期數據
        function checkSession(event) {
            const sessionData = sessionStorage.getItem('currentSession');
            if (!sessionData) {
                event.preventDefault();
                alert('請先在首頁創建修行項目並開始會期');
                window.location.href = 'index.html';
            }
        }

        // 檢查完成數據
        function checkCompletion(event) {
            const completionData = sessionStorage.getItem('completionData');
            if (!completionData) {
                event.preventDefault();
                alert('請先完成修行會期才能進行功德迴向');
                window.location.href = 'index.html';
            }
        }

        // 添加滑鼠跟隨效果
        document.addEventListener('mousemove', (e) => {
            const circles = document.querySelectorAll('.bg-circle');
            circles.forEach((circle, index) => {
                const speed = (index + 1) * 0.02;
                const x = e.clientX * speed;
                const y = e.clientY * speed;
                circle.style.transform = `translate(${x}px, ${y}px)`;
            });
        });

        // 添加觸摸反饋
        document.querySelectorAll('.nav-card').forEach(card => {
            card.addEventListener('touchstart', () => {
                card.style.transform = 'scale(0.95)';
            });
            
            card.addEventListener('touchend', () => {
                setTimeout(() => {
                    card.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
