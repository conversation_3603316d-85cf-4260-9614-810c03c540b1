// Fitnest App JavaScript

class FitnestApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.animateProgressRings();
        this.updateDateTime();
        this.loadUserData();
        this.setupNavigation();
    }

    setupEventListeners() {
        // 底部導航點擊事件
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                this.handleNavigation(e.currentTarget);
            });
        });

        // 運動卡片點擊事件
        const workoutCards = document.querySelectorAll('.workout-card');
        workoutCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.handleWorkoutSelection(e.currentTarget);
            });
        });

        // 運動動作按鈕點擊事件
        const actionButtons = document.querySelectorAll('.workout-action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.startWorkout(e.currentTarget);
            });
        });

        // 狀態卡片點擊事件
        const statusCards = document.querySelectorAll('.status-card');
        statusCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.showStatusDetail(e.currentTarget);
            });
        });

        // 通知按鈕點擊事件
        const notificationBtn = document.querySelector('.notification-btn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.showNotifications();
            });
        }

        // 個人資料按鈕點擊事件
        const profileBtn = document.querySelector('.profile-btn');
        if (profileBtn) {
            profileBtn.addEventListener('click', () => {
                this.showProfile();
            });
        }

        // 查看更多按鈕點擊事件
        const seeMoreBtn = document.querySelector('.see-more-btn');
        if (seeMoreBtn) {
            seeMoreBtn.addEventListener('click', () => {
                this.showAllWorkouts();
            });
        }
    }

    handleNavigation(navItem) {
        // 移除所有活動狀態
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // 添加活動狀態到點擊的項目
        navItem.classList.add('active');

        // 獲取導航標籤
        const label = navItem.querySelector('.nav-label').textContent;
        
        // 根據導航項目執行相應動作
        switch(label) {
            case '首頁':
                this.showHome();
                break;
            case '活動':
                this.showActivity();
                break;
            case '搜索':
                this.showSearch();
                break;
            case '相機':
                this.showCamera();
                break;
            case '個人資料':
                this.showProfile();
                break;
        }

        // 添加點擊動畫
        this.addClickAnimation(navItem);
    }

    handleWorkoutSelection(card) {
        // 移除所有運動卡片的活動狀態
        document.querySelectorAll('.workout-card').forEach(c => {
            c.classList.remove('active');
        });

        // 添加活動狀態到選中的卡片
        card.classList.add('active');

        // 添加選擇動畫
        this.addClickAnimation(card);
    }

    startWorkout(button) {
        const workoutCard = button.closest('.workout-card');
        const workoutTitle = workoutCard.querySelector('h4').textContent;
        
        // 顯示開始運動的確認
        this.showWorkoutModal(workoutTitle);
        
        // 添加點擊動畫
        this.addClickAnimation(button);
    }

    showStatusDetail(card) {
        const cardTitle = card.querySelector('.card-title').textContent;
        const cardValue = card.querySelector('.card-value').textContent;
        
        // 顯示狀態詳情模態框
        this.showStatusModal(cardTitle, cardValue);
        
        // 添加點擊動畫
        this.addClickAnimation(card);
    }

    animateProgressRings() {
        const progressRings = document.querySelectorAll('.progress-fill');
        
        progressRings.forEach(ring => {
            const progress = ring.style.getPropertyValue('--progress');
            if (progress) {
                // 動畫進度環
                ring.style.strokeDashoffset = '283';
                setTimeout(() => {
                    ring.style.strokeDashoffset = `calc(283 - (283 * ${progress}) / 100)`;
                }, 500);
            }
        });
    }

    updateDateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-TW', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // 更新時間顯示（如果有的話）
        const timeElement = document.querySelector('.current-time');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    loadUserData() {
        // 模擬從 API 加載用戶數據
        const userData = {
            name: '用戶',
            todayCalories: 760,
            targetCalories: 900,
            heartRate: 78,
            waterIntake: 2.5,
            sleepHours: 7.5,
            totalCalories: 1847
        };

        this.updateUserInterface(userData);
    }

    updateUserInterface(data) {
        // 更新今日目標進度
        const targetValue = document.querySelector('.target-value');
        if (targetValue) {
            targetValue.textContent = `${data.todayCalories} / ${data.targetCalories}`;
        }

        const progressFill = document.querySelector('.progress-fill');
        if (progressFill) {
            const percentage = (data.todayCalories / data.targetCalories * 100).toFixed(1);
            progressFill.style.setProperty('--progress', `${percentage}%`);
            
            const progressText = document.querySelector('.progress-text');
            if (progressText) {
                progressText.textContent = `${Math.round(percentage)}%`;
            }
        }

        // 更新水分攝取進度
        const waterFill = document.querySelector('.water-fill');
        if (waterFill) {
            const waterPercentage = (data.waterIntake / 4 * 100).toFixed(1);
            waterFill.style.setProperty('--fill', `${waterPercentage}%`);
        }
    }

    setupNavigation() {
        // 設置頁面路由（簡化版）
        this.currentPage = 'home';
        this.pages = {
            home: document.querySelector('.main-content'),
            activity: null,
            search: null,
            camera: null,
            profile: null
        };
    }

    showHome() {
        console.log('顯示首頁');
        this.currentPage = 'home';
    }

    showActivity() {
        console.log('顯示活動頁面');
        this.currentPage = 'activity';
        this.showToast('活動頁面開發中...');
    }

    showSearch() {
        console.log('顯示搜索頁面');
        this.currentPage = 'search';
        this.showToast('搜索功能開發中...');
    }

    showCamera() {
        console.log('顯示相機頁面');
        this.currentPage = 'camera';
        this.showToast('相機功能開發中...');
    }

    showProfile() {
        console.log('顯示個人資料頁面');
        this.currentPage = 'profile';
        this.showToast('個人資料頁面開發中...');
    }

    showNotifications() {
        console.log('顯示通知');
        this.showToast('暫無新通知');
    }

    showAllWorkouts() {
        console.log('顯示所有運動');
        this.showToast('查看所有運動功能開發中...');
    }

    showWorkoutModal(workoutTitle) {
        const modal = this.createModal(
            '開始運動',
            `確定要開始 ${workoutTitle} 嗎？`,
            [
                { text: '取消', action: () => this.closeModal() },
                { text: '開始', action: () => this.confirmStartWorkout(workoutTitle) }
            ]
        );
        document.body.appendChild(modal);
    }

    showStatusModal(title, value) {
        const modal = this.createModal(
            title,
            `當前數值：${value}`,
            [
                { text: '關閉', action: () => this.closeModal() }
            ]
        );
        document.body.appendChild(modal);
    }

    createModal(title, content, buttons) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>${title}</h3>
                <p>${content}</p>
                <div class="modal-buttons">
                    ${buttons.map(btn => `<button class="modal-btn" data-action="${btn.text}">${btn.text}</button>`).join('')}
                </div>
            </div>
        `;

        // 添加按鈕事件監聽器
        buttons.forEach(btn => {
            const buttonElement = modal.querySelector(`[data-action="${btn.text}"]`);
            buttonElement.addEventListener('click', btn.action);
        });

        // 點擊背景關閉模態框
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });

        return modal;
    }

    closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    }

    confirmStartWorkout(workoutTitle) {
        this.closeModal();
        this.showToast(`開始 ${workoutTitle}！`);
        // 這裡可以添加實際的運動開始邏輯
    }

    showToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        document.body.appendChild(toast);

        // 顯示動畫
        setTimeout(() => toast.classList.add('show'), 100);

        // 隱藏動畫
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    addClickAnimation(element) {
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }
}

// 初始化應用
document.addEventListener('DOMContentLoaded', () => {
    new FitnestApp();
});

// 添加一些實用工具函數
const utils = {
    formatTime: (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    },

    formatCalories: (calories) => {
        return calories.toLocaleString('zh-TW');
    },

    calculateBMI: (weight, height) => {
        return (weight / ((height / 100) ** 2)).toFixed(1);
    },

    getGreeting: () => {
        const hour = new Date().getHours();
        if (hour < 12) return '早安';
        if (hour < 18) return '午安';
        return '晚安';
    }
};

// 導出工具函數供其他模塊使用
window.FitnestUtils = utils;
