import { StorageService } from '../StorageService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { mockProject, mockSession, mockWish, mockBlessing } from '../../test/setup';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage');

describe('StorageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.clear as jest.Mock).mockClear();
  });

  describe('Project Management', () => {
    it('should add a new project', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('[]');
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const projectData = {
        title: '六字大明咒',
        description: '觀音菩薩心咒',
        totalGoalCount: 1000000,
        currentTotalCount: 0,
      };

      const result = await StorageService.addProject(projectData);

      expect(result).toBeTruthy();
      expect(result?.title).toBe(projectData.title);
      expect(result?.id).toBeDefined();
      expect(result?.createdAt).toBeDefined();
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should get all projects', async () => {
      const projects = [mockProject];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(projects));

      const result = await StorageService.getProjects();

      expect(result).toEqual(projects);
      expect(AsyncStorage.getItem).toHaveBeenCalledWith('mantra_projects');
    });

    it('should update a project', async () => {
      const projects = [mockProject];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(projects));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const updates = { currentTotalCount: 60000 };
      const result = await StorageService.updateProject(mockProject.id, updates);

      expect(result).toBe(true);
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should delete a project', async () => {
      const projects = [mockProject];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(projects));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const result = await StorageService.deleteProject(mockProject.id);

      expect(result).toBe(true);
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should get project by id', async () => {
      const projects = [mockProject];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(projects));

      const result = await StorageService.getProject(mockProject.id);

      expect(result).toEqual(mockProject);
    });
  });

  describe('Session Management', () => {
    it('should add a new session', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('[]');
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const sessionData = {
        projectId: mockProject.id,
        count: 108,
        target: 108,
        duration: 600,
        startTime: '2024-01-01T10:00:00.000Z',
        endTime: '2024-01-01T10:10:00.000Z',
        completed: true,
      };

      const result = await StorageService.addSession(sessionData);

      expect(result).toBeTruthy();
      expect(result?.count).toBe(sessionData.count);
      expect(result?.id).toBeDefined();
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should get sessions by project', async () => {
      const sessions = [mockSession];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(sessions));

      const result = await StorageService.getSessionsByProject(mockProject.id);

      expect(result).toEqual(sessions);
    });

    it('should get recent sessions', async () => {
      const sessions = [mockSession];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(sessions));

      const result = await StorageService.getRecentSessions(5);

      expect(result).toEqual(sessions);
    });
  });

  describe('Wish Management', () => {
    it('should add a new wish', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('[]');
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const wishData = {
        text: '願家人身體健康',
        category: 'health' as const,
      };

      const result = await StorageService.addWish(wishData);

      expect(result).toBeTruthy();
      expect(result?.text).toBe(wishData.text);
      expect(result?.blessedCount).toBe(0);
      expect(result?.id).toBeDefined();
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should update a wish', async () => {
      const wishes = [mockWish];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(wishes));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const updates = { text: '願家人平安健康' };
      const result = await StorageService.updateWish(mockWish.id, updates);

      expect(result).toBe(true);
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should delete a wish', async () => {
      const wishes = [mockWish];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(wishes));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const result = await StorageService.deleteWish(mockWish.id);

      expect(result).toBe(true);
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });
  });

  describe('Blessing Management', () => {
    it('should add a new blessing', async () => {
      // Mock wishes and blessings
      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce('[]') // blessings
        .mockResolvedValueOnce(JSON.stringify([mockWish])); // wishes for updating blessed count
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const blessingData = {
        projectId: mockProject.id,
        count: 108,
        wishIds: [mockWish.id],
        dedicationText: '願以此功德，迴向法界眾生',
        sessionData: mockSession,
      };

      const result = await StorageService.addBlessing(blessingData);

      expect(result).toBeTruthy();
      expect(result?.count).toBe(blessingData.count);
      expect(result?.wishIds).toEqual(blessingData.wishIds);
      expect(AsyncStorage.setItem).toHaveBeenCalledTimes(2); // blessings + wishes
    });
  });

  describe('Statistics', () => {
    it('should get today stats', async () => {
      const today = new Date().toDateString();
      const todaySession = {
        ...mockSession,
        createdAt: new Date().toISOString(),
      };
      const sessions = [todaySession];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(sessions));

      const result = await StorageService.getTodayStats();

      expect(result.sessionCount).toBe(1);
      expect(result.totalCount).toBe(todaySession.count);
      expect(result.totalDuration).toBe(todaySession.duration);
    });

    it('should get project stats', async () => {
      const sessions = [mockSession];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(sessions));

      const result = await StorageService.getProjectStats(mockProject.id);

      expect(result.sessionCount).toBe(1);
      expect(result.totalCount).toBe(mockSession.count);
      expect(result.totalDuration).toBe(mockSession.duration);
      expect(result.avgSessionCount).toBe(mockSession.count);
    });

    it('should get all stats', async () => {
      const projects = [mockProject];
      const sessions = [mockSession];
      const wishes = [mockWish];
      const blessings = [mockBlessing];

      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce(JSON.stringify(projects))
        .mockResolvedValueOnce(JSON.stringify(sessions))
        .mockResolvedValueOnce(JSON.stringify(wishes))
        .mockResolvedValueOnce(JSON.stringify(blessings));

      const result = await StorageService.getAllStats();

      expect(result.projectCount).toBe(1);
      expect(result.sessionCount).toBe(1);
      expect(result.totalCount).toBe(mockSession.count);
      expect(result.wishCount).toBe(1);
      expect(result.blessedWishCount).toBe(1);
      expect(result.blessingCount).toBe(1);
    });
  });

  describe('Data Export/Import', () => {
    it('should export data', async () => {
      const projects = [mockProject];
      const sessions = [mockSession];
      const wishes = [mockWish];
      const blessings = [mockBlessing];
      const settings = { soundEnabled: true, vibrationEnabled: true };

      (AsyncStorage.getItem as jest.Mock)
        .mockResolvedValueOnce(JSON.stringify(projects))
        .mockResolvedValueOnce(JSON.stringify(sessions))
        .mockResolvedValueOnce(JSON.stringify(wishes))
        .mockResolvedValueOnce(JSON.stringify(blessings))
        .mockResolvedValueOnce(JSON.stringify(settings));

      const result = await StorageService.exportData();

      expect(result).toBeTruthy();
      expect(result?.projects).toEqual(projects);
      expect(result?.sessions).toEqual(sessions);
      expect(result?.wishes).toEqual(wishes);
      expect(result?.blessings).toEqual(blessings);
      expect(result?.exportedAt).toBeDefined();
      expect(result?.version).toBeDefined();
    });

    it('should import data', async () => {
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      const importData = {
        projects: [mockProject],
        sessions: [mockSession],
        wishes: [mockWish],
        blessings: [mockBlessing],
        settings: { soundEnabled: true, vibrationEnabled: true },
        exportedAt: '2024-01-01T00:00:00.000Z',
        version: '1.0.0',
      };

      const result = await StorageService.importData(importData);

      expect(result).toBe(true);
      expect(AsyncStorage.setItem).toHaveBeenCalledTimes(5);
    });
  });

  describe('Utility Functions', () => {
    it('should format duration correctly', () => {
      expect(StorageService.formatDuration(30)).toBe('30秒');
      expect(StorageService.formatDuration(90)).toBe('1分30秒');
      expect(StorageService.formatDuration(3661)).toBe('1小時1分鐘');
    });

    it('should format date correctly', () => {
      const today = new Date();
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const twoDaysAgo = new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000);

      expect(StorageService.formatDate(today.toISOString())).toBe('今天');
      expect(StorageService.formatDate(yesterday.toISOString())).toBe('昨天');
      expect(StorageService.formatDate(twoDaysAgo.toISOString())).toBe('1天前');
    });
  });

  describe('Error Handling', () => {
    it('should handle storage errors gracefully', async () => {
      (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));

      const result = await StorageService.getProjects();

      expect(result).toEqual([]);
    });

    it('should handle save errors gracefully', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('[]');
      (AsyncStorage.setItem as jest.Mock).mockRejectedValue(new Error('Save error'));

      const result = await StorageService.addProject({
        title: 'Test',
        description: '',
        totalGoalCount: 1000,
        currentTotalCount: 0,
      });

      expect(result).toBeNull();
    });
  });
});
