<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fitnest - 健身應用啟動頁</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(315deg, #92A3FD 0%, #9DCEFF 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }

        .logo {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .nav-card {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .nav-card h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .nav-card p {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .features {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            margin-top: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .features h3 {
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            opacity: 0.9;
        }

        .feature-item::before {
            content: "✓";
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .tech-stack {
            margin-top: 2rem;
            opacity: 0.8;
        }

        .tech-stack h4 {
            margin-bottom: 0.5rem;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }

        .tech-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
            
            .feature-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1 class="logo">Fitnest</h1>
        <p class="subtitle">現代化健身應用 UI 套件</p>
        
        <div class="nav-grid">
            <a href="index.html" class="nav-card">
                <h3>🏠 主頁面</h3>
                <p>完整的健身應用主界面，包含活動追蹤、目標設定和運動推薦</p>
            </a>
            
            <a href="activity.html" class="nav-card">
                <h3>📊 活動頁面</h3>
                <p>詳細的活動統計、圖表分析和運動歷史記錄</p>
            </a>
            
            <a href="components.html" class="nav-card">
                <h3>🧩 組件展示</h3>
                <p>所有 UI 組件的展示頁面，包含按鈕、卡片、圖表等</p>
            </a>
        </div>

        <div class="features">
            <h3>✨ 功能特色</h3>
            <div class="feature-list">
                <div class="feature-item">響應式設計</div>
                <div class="feature-item">現代化 UI</div>
                <div class="feature-item">流暢動畫</div>
                <div class="feature-item">數據可視化</div>
                <div class="feature-item">交互式組件</div>
                <div class="feature-item">模塊化架構</div>
                <div class="feature-item">無障礙支持</div>
                <div class="feature-item">跨瀏覽器兼容</div>
            </div>
            
            <div class="tech-stack">
                <h4>🛠️ 技術棧</h4>
                <div class="tech-tags">
                    <span class="tech-tag">HTML5</span>
                    <span class="tech-tag">CSS3</span>
                    <span class="tech-tag">JavaScript ES6+</span>
                    <span class="tech-tag">CSS Grid</span>
                    <span class="tech-tag">Flexbox</span>
                    <span class="tech-tag">CSS Variables</span>
                    <span class="tech-tag">SVG</span>
                    <span class="tech-tag">Responsive Design</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加一些簡單的交互效果
        document.addEventListener('DOMContentLoaded', () => {
            const cards = document.querySelectorAll('.nav-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 添加載入動畫
            const container = document.querySelector('.container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.8s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
