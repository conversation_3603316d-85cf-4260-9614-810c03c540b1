import { NavigatorScreenParams } from '@react-navigation/native';
import { Project, Session } from './index';

// 主堆疊導航參數
export type RootStackParamList = {
  Welcome: undefined;
  Main: NavigatorScreenParams<TabParamList>;
  Counter: {
    projectId: string;
    projectTitle: string;
    target: number;
  };
  Merit: {
    sessionData: {
      projectId: string;
      projectTitle: string;
      count: number;
      target: number;
      duration: number;
      completedAt: string;
    };
  };
  ProjectDetail: {
    projectId: string;
  };
  CreateProject: undefined;
  EditProject: {
    project: Project;
  };
  Settings: undefined;
  About: undefined;
  Privacy: undefined;
  Terms: undefined;
};

// 底部標籤導航參數
export type TabParamList = {
  Home: undefined;
  WishList: undefined;
  History: undefined;
  Stats: undefined;
};

// 首頁堆疊導航參數
export type HomeStackParamList = {
  HomeMain: undefined;
  ProjectDetail: {
    projectId: string;
  };
  CreateProject: undefined;
  EditProject: {
    project: Project;
  };
};

// 願望清單堆疊導航參數
export type WishListStackParamList = {
  WishListMain: undefined;
  CreateWish: undefined;
  EditWish: {
    wishId: string;
  };
  WishDetail: {
    wishId: string;
  };
};

// 歷史記錄堆疊導航參數
export type HistoryStackParamList = {
  HistoryMain: undefined;
  SessionDetail: {
    session: Session;
  };
  ProjectHistory: {
    projectId: string;
    projectTitle: string;
  };
};

// 統計頁面堆疊導航參數
export type StatsStackParamList = {
  StatsMain: undefined;
  DetailedStats: {
    type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  };
  ProjectStats: {
    projectId: string;
    projectTitle: string;
  };
};

// 設置頁面堆疊導航參數
export type SettingsStackParamList = {
  SettingsMain: undefined;
  GeneralSettings: undefined;
  NotificationSettings: undefined;
  DataManagement: undefined;
  About: undefined;
  Privacy: undefined;
  Terms: undefined;
  Help: undefined;
};

// 導航 Props 類型輔助
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';

// 主堆疊屏幕 Props
export type RootStackScreenProps<T extends keyof RootStackParamList> = 
  NativeStackScreenProps<RootStackParamList, T>;

// 底部標籤屏幕 Props
export type TabScreenProps<T extends keyof TabParamList> = 
  BottomTabScreenProps<TabParamList, T>;

// 首頁堆疊屏幕 Props
export type HomeStackScreenProps<T extends keyof HomeStackParamList> = 
  NativeStackScreenProps<HomeStackParamList, T>;

// 願望清單堆疊屏幕 Props
export type WishListStackScreenProps<T extends keyof WishListStackParamList> = 
  NativeStackScreenProps<WishListStackParamList, T>;

// 歷史記錄堆疊屏幕 Props
export type HistoryStackScreenProps<T extends keyof HistoryStackParamList> = 
  NativeStackScreenProps<HistoryStackParamList, T>;

// 統計頁面堆疊屏幕 Props
export type StatsStackScreenProps<T extends keyof StatsStackParamList> = 
  NativeStackScreenProps<StatsStackParamList, T>;

// 設置頁面堆疊屏幕 Props
export type SettingsStackScreenProps<T extends keyof SettingsStackParamList> = 
  NativeStackScreenProps<SettingsStackParamList, T>;

// 組合導航 Props（用於嵌套導航）
export type CompositeScreenProps<
  T extends Record<string, object | undefined>,
  U extends Record<string, object | undefined>
> = T & {
  navigation: T['navigation'] & U['navigation'];
  route: T['route'] & U['route'];
};

// 常用組合類型
export type HomeTabScreenProps = CompositeScreenProps<
  TabScreenProps<'Home'>,
  RootStackScreenProps<keyof RootStackParamList>
>;

export type WishListTabScreenProps = CompositeScreenProps<
  TabScreenProps<'WishList'>,
  RootStackScreenProps<keyof RootStackParamList>
>;

export type HistoryTabScreenProps = CompositeScreenProps<
  TabScreenProps<'History'>,
  RootStackScreenProps<keyof RootStackParamList>
>;

export type StatsTabScreenProps = CompositeScreenProps<
  TabScreenProps<'Stats'>,
  RootStackScreenProps<keyof RootStackParamList>
>;

// 導航狀態類型
export interface NavigationState {
  currentRoute: string;
  previousRoute?: string;
  params?: any;
}

// 深度鏈接類型
export interface DeepLinkConfig {
  screens: {
    Welcome: string;
    Main: {
      screens: {
        Home: string;
        WishList: string;
        History: string;
        Stats: string;
      };
    };
    Counter: string;
    Merit: string;
    Settings: string;
  };
}

// 導航動畫配置類型
export interface NavigationAnimationConfig {
  gestureEnabled?: boolean;
  animationTypeForReplace?: 'push' | 'pop';
  animation?: 'default' | 'fade' | 'slide_from_right' | 'slide_from_left' | 'slide_from_bottom';
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
