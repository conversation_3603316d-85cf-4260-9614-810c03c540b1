import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Share,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Colors } from '../constants/Colors';
import { AudioService } from '../services/AudioService';
import { StorageService } from '../services/StorageService';
import { RootStackScreenProps } from '../types/navigation';
import { Wish, Project, ProjectStats } from '../types';

type MeritScreenProps = RootStackScreenProps<'Merit'>;

const MeritScreen: React.FC<MeritScreenProps> = ({ navigation, route }) => {
  const { sessionData } = route.params;
  
  const [wishes, setWishes] = useState<Wish[]>([]);
  const [selectedWishIds, setSelectedWishIds] = useState<string[]>([]);
  const [dedicationText, setDedicationText] = useState('');
  const [project, setProject] = useState<Project | null>(null);
  const [totalStats, setTotalStats] = useState<ProjectStats | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const templates = {
    universal: `願以此功德，莊嚴佛淨土。
上報四重恩，下濟三途苦。
若有見聞者，悉發菩提心。
盡此一報身，同生極樂國。`,
    family: `願以此功德，迴向我的家人。
願家人身體健康，平安喜樂。
願家庭和睦，福慧增長。
願所有眷屬，離苦得樂。`,
    health: `願以此功德，迴向身心健康。
願身體強健，遠離病苦。
願心靈清淨，智慧增長。
願眾生皆得，無病無憂。`,
  };

  useEffect(() => {
    loadData();
    setDefaultDedication();
  }, []);

  const loadData = async () => {
    try {
      const [wishesData, projectData, statsData] = await Promise.all([
        StorageService.getWishes(),
        StorageService.getProject(sessionData.projectId),
        StorageService.getProjectStats(sessionData.projectId),
      ]);
      
      setWishes(wishesData);
      setProject(projectData);
      setTotalStats(statsData);
    } catch (error) {
      console.error('Failed to load merit data:', error);
    }
  };

  const setDefaultDedication = () => {
    const defaultText = `我剛剛完成了「${sessionData.projectTitle}」的修行，本次持誦 ${sessionData.count} 次。

願以此功德，迴向法界眾生，願眾生離苦得樂，早證菩提。`;
    
    setDedicationText(defaultText);
  };

  const handleWishToggle = async (wishId: string) => {
    await AudioService.buttonPress();
    
    setSelectedWishIds(prev => {
      if (prev.includes(wishId)) {
        return prev.filter(id => id !== wishId);
      } else {
        return [...prev, wishId];
      }
    });
  };

  const useTemplate = async (templateKey: keyof typeof templates) => {
    await AudioService.buttonPress();
    
    if (dedicationText.trim() && dedicationText !== templates[templateKey]) {
      Alert.alert(
        '替換迴向文',
        '確定要替換當前的迴向文嗎？',
        [
          { text: '取消', style: 'cancel' },
          {
            text: '替換',
            onPress: () => setDedicationText(templates[templateKey]),
          },
        ]
      );
    } else {
      setDedicationText(templates[templateKey]);
    }
  };

  const handleInfuseWishes = async () => {
    if (selectedWishIds.length === 0) {
      Alert.alert('提示', '請先選擇要灌注的願望');
      return;
    }

    setIsSubmitting(true);
    await AudioService.buttonPress();

    try {
      // 創建祝福記錄
      const blessing = {
        projectId: sessionData.projectId,
        count: sessionData.count,
        wishIds: selectedWishIds,
        dedicationText: dedicationText.trim(),
        sessionData,
      };

      const newBlessing = await StorageService.addBlessing(blessing);
      
      if (newBlessing) {
        await AudioService.buttonSuccess();
        
        Alert.alert(
          '🎉 功德灌注成功',
          `已將修行功德灌注到 ${selectedWishIds.length} 個願望中`,
          [
            {
              text: '確定',
              onPress: () => {
                // 重新加載願望數據以更新祝福計數
                loadData();
                setSelectedWishIds([]);
              },
            },
          ]
        );
      } else {
        await AudioService.buttonError();
        Alert.alert('錯誤', '功德灌注失敗，請重試');
      }
    } catch (error) {
      console.error('Failed to infuse wishes:', error);
      await AudioService.buttonError();
      Alert.alert('錯誤', '功德灌注失敗，請重試');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleShare = async () => {
    if (!dedicationText.trim()) {
      Alert.alert('提示', '請先編寫迴向文');
      return;
    }

    await AudioService.buttonPress();

    try {
      await Share.share({
        message: dedicationText,
        title: '修行功德迴向',
      });
    } catch (error) {
      console.error('Failed to share:', error);
      Alert.alert('錯誤', '分享失敗，請重試');
    }
  };

  const handleComplete = async () => {
    await AudioService.buttonPress();
    navigation.navigate('Main');
  };

  const getTotalProgress = () => {
    if (!project || !totalStats) return '-- / --';
    return `${totalStats.totalCount.toLocaleString()} / ${project.totalGoalCount.toLocaleString()}`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 頂部導航 */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.textSecondary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>功德迴向</Text>
          <View style={styles.headerPlaceholder} />
        </View>

        {/* 完成慶祝 */}
        <View style={styles.celebrationSection}>
          <LinearGradient
            colors={Colors.gradientSuccess}
            style={styles.celebrationGradient}
          >
            <View style={styles.celebrationIcon}>
              <Ionicons name="star" size={40} color={Colors.white} />
            </View>
            <Text style={styles.celebrationTitle}>🎉 修行圓滿</Text>
            <Text style={styles.celebrationSubtitle}>恭喜您完成了本次修行會期</Text>
          </LinearGradient>
        </View>

        {/* 修行摘要 */}
        <View style={styles.summarySection}>
          <Text style={styles.sectionTitle}>本次修行摘要</Text>
          <View style={styles.summaryCard}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>修行項目</Text>
              <Text style={styles.summaryValue}>{sessionData.projectTitle}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>持誦次數</Text>
              <Text style={[styles.summaryValue, styles.summaryHighlight]}>
                {sessionData.count}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>用時</Text>
              <Text style={styles.summaryValue}>
                {StorageService.formatDuration(sessionData.duration)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>總進度</Text>
              <Text style={styles.summaryValue}>{getTotalProgress()}</Text>
            </View>
          </View>
        </View>

        {/* 迴向文編輯 */}
        <View style={styles.dedicationSection}>
          <Text style={styles.sectionTitle}>功德迴向</Text>
          <TextInput
            style={styles.dedicationInput}
            value={dedicationText}
            onChangeText={setDedicationText}
            placeholder="編輯您的迴向文..."
            placeholderTextColor={Colors.textTertiary}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
          />
          
          {/* 模板按鈕 */}
          <View style={styles.templatesSection}>
            <Text style={styles.templatesTitle}>常用迴向文</Text>
            <View style={styles.templatesGrid}>
              <TouchableOpacity
                style={styles.templateButton}
                onPress={() => useTemplate('universal')}
              >
                <Text style={styles.templateTitle}>普迴向</Text>
                <Text style={styles.templatePreview}>願以此功德，莊嚴佛淨土...</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.templateButton}
                onPress={() => useTemplate('family')}
              >
                <Text style={styles.templateTitle}>家庭迴向</Text>
                <Text style={styles.templatePreview}>願以此功德，迴向我的家人...</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.templateButton}
                onPress={() => useTemplate('health')}
              >
                <Text style={styles.templateTitle}>健康迴向</Text>
                <Text style={styles.templatePreview}>願以此功德，迴向身心健康...</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* 願望灌注 */}
        <View style={styles.wishesSection}>
          <Text style={styles.sectionTitle}>灌注願望</Text>
          <Text style={styles.sectionDescription}>
            選擇要灌注功德的願望，讓修行的力量加持您的心願
          </Text>
          
          {wishes.length === 0 ? (
            <View style={styles.emptyWishes}>
              <Ionicons name="heart-outline" size={48} color={Colors.gray300} />
              <Text style={styles.emptyWishesText}>還沒有願望可以灌注</Text>
              <TouchableOpacity
                style={styles.emptyWishesButton}
                onPress={() => navigation.navigate('Main', { screen: 'WishList' })}
              >
                <Text style={styles.emptyWishesButtonText}>前往願望清單</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.wishesList}>
              {wishes.map((wish) => (
                <TouchableOpacity
                  key={wish.id}
                  style={[
                    styles.wishItem,
                    selectedWishIds.includes(wish.id) && styles.wishItemSelected,
                  ]}
                  onPress={() => handleWishToggle(wish.id)}
                >
                  <Text style={styles.wishText}>{wish.text}</Text>
                  <Text style={styles.wishDate}>
                    {StorageService.formatDate(wish.createdAt)}
                  </Text>
                  
                  {selectedWishIds.includes(wish.id) && (
                    <View style={styles.selectedBadge}>
                      <Ionicons name="checkmark-circle" size={20} color={Colors.primary} />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* 操作按鈕 */}
        <View style={styles.actionsSection}>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[
                styles.actionButton,
                selectedWishIds.length === 0 && styles.actionButtonDisabled,
              ]}
              onPress={handleInfuseWishes}
              disabled={selectedWishIds.length === 0 || isSubmitting}
            >
              <LinearGradient
                colors={selectedWishIds.length > 0 ? Colors.gradientPrimary : [Colors.gray200, Colors.gray300]}
                style={styles.actionButtonGradient}
              >
                <Ionicons
                  name="heart"
                  size={20}
                  color={selectedWishIds.length > 0 ? Colors.white : Colors.textTertiary}
                />
                <Text
                  style={[
                    styles.actionButtonText,
                    selectedWishIds.length === 0 && styles.actionButtonTextDisabled,
                  ]}
                >
                  灌注到願望清單
                </Text>
                {selectedWishIds.length > 0 && (
                  <View style={styles.selectedCount}>
                    <Text style={styles.selectedCountText}>{selectedWishIds.length}</Text>
                  </View>
                )}
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleShare}
            >
              <LinearGradient
                colors={Colors.gradientSecondary}
                style={styles.actionButtonGradient}
              >
                <Ionicons name="share" size={20} color={Colors.white} />
                <Text style={styles.actionButtonText}>分享到社群</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity
            style={styles.completeButton}
            onPress={handleComplete}
          >
            <LinearGradient
              colors={Colors.gradientSuccess}
              style={styles.completeButtonGradient}
            >
              <Text style={styles.completeButtonText}>完成並返回</Text>
              <Ionicons name="checkmark" size={20} color={Colors.white} />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
    backgroundColor: Colors.white,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  headerPlaceholder: {
    width: 40,
  },
  celebrationSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  celebrationGradient: {
    borderRadius: 20,
    padding: 32,
    alignItems: 'center',
  },
  celebrationIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  celebrationTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.white,
    marginBottom: 8,
  },
  celebrationSubtitle: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.white,
    opacity: 0.9,
  },
  summarySection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 16,
  },
  summaryCard: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textSecondary,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  summaryHighlight: {
    color: Colors.primary,
    fontSize: 16,
  },
  dedicationSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  dedicationInput: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    fontSize: 14,
    color: Colors.textPrimary,
    textAlignVertical: 'top',
    minHeight: 120,
    borderWidth: 2,
    borderColor: Colors.border,
  },
  templatesSection: {
    marginTop: 16,
  },
  templatesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 12,
  },
  templatesGrid: {
    gap: 8,
  },
  templateButton: {
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 12,
    padding: 12,
    borderWidth: 2,
    borderColor: Colors.border,
  },
  templateTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  templatePreview: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textSecondary,
    lineHeight: 16,
  },
  wishesSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionDescription: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textSecondary,
    marginBottom: 16,
    lineHeight: 20,
  },
  emptyWishes: {
    alignItems: 'center',
    paddingVertical: 32,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
  },
  emptyWishesText: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textSecondary,
    marginVertical: 16,
  },
  emptyWishesButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 12,
  },
  emptyWishesButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
  },
  wishesList: {
    gap: 12,
  },
  wishItem: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: Colors.border,
    position: 'relative',
  },
  wishItemSelected: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primaryLight,
  },
  wishText: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textPrimary,
    lineHeight: 20,
    marginBottom: 8,
  },
  wishDate: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textTertiary,
  },
  selectedBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
  },
  actionsSection: {
    paddingHorizontal: 20,
    paddingBottom: 32,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
  },
  actionButtonDisabled: {
    opacity: 0.5,
  },
  actionButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
    position: 'relative',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
  },
  actionButtonTextDisabled: {
    color: Colors.textTertiary,
  },
  selectedCount: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: Colors.error,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedCountText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.white,
  },
  completeButton: {
    width: '100%',
  },
  completeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
  },
});

export default MeritScreen;
