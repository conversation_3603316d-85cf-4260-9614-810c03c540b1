// 計數頁面邏輯

class CounterPage {
    constructor() {
        this.sessionData = null;
        this.currentCount = 0;
        this.startTime = null;
        this.timer = null;
        this.isPaused = false;
        this.init();
    }

    init() {
        this.loadSessionData();
        this.setupUI();
        this.startTimer();
        this.setupEventListeners();
    }

    loadSessionData() {
        const sessionDataStr = sessionStorage.getItem('currentSession');
        if (!sessionDataStr) {
            MantraUtils.showToast('會期數據丟失，返回首頁');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
            return;
        }

        this.sessionData = JSON.parse(sessionDataStr);
        this.startTime = new Date(this.sessionData.startTime);
    }

    setupUI() {
        if (!this.sessionData) return;

        // 設置項目標題
        const titleElement = document.getElementById('counterProjectTitle');
        if (titleElement) {
            titleElement.textContent = this.sessionData.projectTitle;
        }

        // 設置目標次數
        const targetElement = document.getElementById('sessionTarget');
        if (targetElement) {
            targetElement.textContent = this.sessionData.target;
        }

        // 設置總進度
        this.updateTotalProgress();

        // 設置開始時間
        const startTimeElement = document.getElementById('startTime');
        if (startTimeElement) {
            startTimeElement.textContent = this.startTime.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 初始化進度環
        this.updateProgressRing();
    }

    updateTotalProgress() {
        const project = storage.getProject(this.sessionData.projectId);
        if (!project) return;

        const stats = storage.getProjectStats(this.sessionData.projectId);
        const totalProgress = `${stats.totalCount.toLocaleString()} / ${project.totalGoalCount.toLocaleString()}`;
        const percentage = project.totalGoalCount > 0 
            ? ((stats.totalCount / project.totalGoalCount) * 100).toFixed(3)
            : 0;

        const progressElement = document.getElementById('totalProgress');
        const percentageElement = document.getElementById('progressPercentage');

        if (progressElement) {
            progressElement.textContent = totalProgress;
        }

        if (percentageElement) {
            percentageElement.textContent = `${percentage}%`;
        }
    }

    updateProgressRing() {
        const ring = document.getElementById('sessionRing');
        const countElement = document.getElementById('sessionCount');
        
        if (!ring || !countElement) return;

        const progress = (this.currentCount / this.sessionData.target) * 100;
        const circumference = 2 * Math.PI * 90; // r=90
        const offset = circumference - (progress / 100) * circumference;

        ring.style.strokeDashoffset = offset;
        countElement.textContent = this.currentCount;

        // 更新減一按鈕狀態
        const subtractBtn = document.getElementById('subtractBtn');
        if (subtractBtn) {
            subtractBtn.disabled = this.currentCount === 0;
        }
    }

    startTimer() {
        this.timer = setInterval(() => {
            if (!this.isPaused) {
                this.updateDuration();
                this.updateAverageSpeed();
            }
        }, 1000);
    }

    updateDuration() {
        const now = new Date();
        const duration = Math.floor((now - this.startTime) / 1000);
        const durationElement = document.getElementById('duration');
        
        if (durationElement) {
            durationElement.textContent = this.formatDuration(duration);
        }
    }

    updateAverageSpeed() {
        if (this.currentCount === 0) return;

        const now = new Date();
        const durationMinutes = (now - this.startTime) / (1000 * 60);
        const speed = Math.round(this.currentCount / durationMinutes);
        
        const speedElement = document.getElementById('avgSpeed');
        if (speedElement) {
            speedElement.textContent = `${speed} /分`;
        }
    }

    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    incrementCounter() {
        if (this.isPaused) return;

        this.currentCount++;
        this.updateProgressRing();
        
        // 播放點擊音效
        MantraUtils.playSound('click');
        
        // 添加波紋效果
        this.addRippleEffect();

        // 檢查是否完成目標
        if (this.currentCount >= this.sessionData.target) {
            this.completeSession();
        }
    }

    addRippleEffect() {
        const ripple = document.getElementById('btnRipple');
        if (ripple) {
            ripple.classList.remove('animate');
            // 強制重繪
            ripple.offsetHeight;
            ripple.classList.add('animate');
        }
    }

    addTen() {
        if (this.isPaused) return;

        for (let i = 0; i < 10; i++) {
            if (this.currentCount < this.sessionData.target) {
                this.currentCount++;
            }
        }
        
        this.updateProgressRing();
        MantraUtils.playSound('click');

        if (this.currentCount >= this.sessionData.target) {
            this.completeSession();
        }
    }

    subtractOne() {
        if (this.isPaused || this.currentCount === 0) return;

        this.currentCount--;
        this.updateProgressRing();
        MantraUtils.playSound('click');
    }

    resetSession() {
        if (this.isPaused) return;

        if (this.currentCount === 0) {
            MantraUtils.showToast('計數已經是 0');
            return;
        }

        if (confirm('確定要重置本次計數嗎？')) {
            this.currentCount = 0;
            this.updateProgressRing();
            MantraUtils.showToast('計數已重置');
        }
    }

    completeSession() {
        // 停止計時器
        if (this.timer) {
            clearInterval(this.timer);
        }

        // 計算持續時間
        const endTime = new Date();
        const duration = Math.floor((endTime - this.startTime) / 1000);

        // 播放完成音效和震動
        MantraUtils.playSound('success');
        MantraUtils.vibrate([200, 100, 200]);

        // 顯示完成模態框
        this.showCompletionModal(duration);
    }

    showCompletionModal(duration) {
        const modal = document.getElementById('celebrationModal');
        const countElement = document.getElementById('completedCount');
        const durationElement = document.getElementById('completedDuration');

        if (modal && countElement && durationElement) {
            countElement.textContent = this.currentCount;
            durationElement.textContent = this.formatDuration(duration);
            modal.style.display = 'flex';
        }
    }

    pauseCounter() {
        this.isPaused = true;
        this.showPauseModal();
    }

    showPauseModal() {
        const modal = document.getElementById('pauseModal');
        const progressElement = document.getElementById('pauseProgress');
        const durationElement = document.getElementById('pauseDuration');

        if (modal && progressElement && durationElement) {
            progressElement.textContent = `${this.currentCount} / ${this.sessionData.target}`;
            
            const now = new Date();
            const duration = Math.floor((now - this.startTime) / 1000);
            durationElement.textContent = this.formatDuration(duration);
            
            modal.style.display = 'flex';
        }
    }

    resumeCounter() {
        this.isPaused = false;
        this.hidePauseModal();
    }

    hidePauseModal() {
        const modal = document.getElementById('pauseModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    saveAndExit() {
        this.saveSession();
        window.location.href = 'index.html';
    }

    saveSession() {
        if (this.currentCount === 0) return;

        const endTime = new Date();
        const duration = Math.floor((endTime - this.startTime) / 1000);

        const session = {
            projectId: this.sessionData.projectId,
            count: this.currentCount,
            target: this.sessionData.target,
            duration: duration,
            startTime: this.startTime.toISOString(),
            endTime: endTime.toISOString(),
            completed: this.currentCount >= this.sessionData.target
        };

        // 保存會期記錄
        storage.addSession(session);

        // 更新項目總計數
        const project = storage.getProject(this.sessionData.projectId);
        if (project) {
            const newTotalCount = (project.currentTotalCount || 0) + this.currentCount;
            storage.updateProject(this.sessionData.projectId, {
                currentTotalCount: newTotalCount
            });
        }
    }

    proceedToMerit() {
        // 保存會期數據
        this.saveSession();
        
        // 保存完成數據到 sessionStorage
        const completionData = {
            projectId: this.sessionData.projectId,
            projectTitle: this.sessionData.projectTitle,
            count: this.currentCount,
            target: this.sessionData.target,
            duration: Math.floor((new Date() - this.startTime) / 1000),
            completedAt: new Date().toISOString()
        };
        
        sessionStorage.setItem('completionData', JSON.stringify(completionData));
        
        // 跳轉到迴向頁面
        window.location.href = 'merit.html';
    }

    continueCounting() {
        // 隱藏慶祝模態框，繼續計數
        const modal = document.getElementById('celebrationModal');
        if (modal) {
            modal.style.display = 'none';
        }
        
        // 重新啟動計時器
        this.startTimer();
    }

    exitCounter() {
        if (this.currentCount > 0) {
            if (confirm('您有未保存的計數，確定要退出嗎？')) {
                this.saveSession();
                window.location.href = 'index.html';
            }
        } else {
            window.location.href = 'index.html';
        }
    }

    setupEventListeners() {
        // 觸摸事件處理
        const counterBtn = document.getElementById('counterBtn');
        if (counterBtn) {
            // 防止雙擊縮放
            counterBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
            });
        }

        // 鍵盤事件
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                this.incrementCounter();
            }
        });
    }
}

// 全局函數
function incrementCounter() {
    if (window.counterPage) {
        window.counterPage.incrementCounter();
    }
}

function addTen() {
    if (window.counterPage) {
        window.counterPage.addTen();
    }
}

function subtractOne() {
    if (window.counterPage) {
        window.counterPage.subtractOne();
    }
}

function resetSession() {
    if (window.counterPage) {
        window.counterPage.resetSession();
    }
}

function pauseCounter() {
    if (window.counterPage) {
        window.counterPage.pauseCounter();
    }
}

function resumeCounter() {
    if (window.counterPage) {
        window.counterPage.resumeCounter();
    }
}

function saveAndExit() {
    if (window.counterPage) {
        window.counterPage.saveAndExit();
    }
}

function exitCounter() {
    if (window.counterPage) {
        window.counterPage.exitCounter();
    }
}

function proceedToMerit() {
    if (window.counterPage) {
        window.counterPage.proceedToMerit();
    }
}

function continueCounting() {
    if (window.counterPage) {
        window.counterPage.continueCounting();
    }
}

function handleCounterTouch() {
    // 觸摸開始處理
}

function handleCounterRelease() {
    // 觸摸結束處理
}

// 初始化計數頁面
document.addEventListener('DOMContentLoaded', () => {
    window.counterPage = new CounterPage();
});
