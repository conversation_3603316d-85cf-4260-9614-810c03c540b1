// 願望清單頁面邏輯

class WishListPage {
    constructor() {
        this.currentFilter = 'all';
        this.editingWishId = null;
        this.init();
    }

    init() {
        this.loadWishStats();
        this.loadWishes();
        this.loadBlessingRecords();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 添加願望表單
        const addForm = document.getElementById('addWishForm');
        if (addForm) {
            addForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addWish();
            });
        }

        // 編輯願望表單
        const editForm = document.getElementById('editWishForm');
        if (editForm) {
            editForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateWish();
            });
        }

        // 篩選按鈕
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });
    }

    loadWishStats() {
        const wishes = storage.getWishes();
        const totalWishes = wishes.length;
        const blessedWishes = wishes.filter(w => w.blessedCount > 0).length;

        const totalElement = document.getElementById('totalWishes');
        const blessedElement = document.getElementById('blessedWishes');

        if (totalElement) {
            totalElement.textContent = totalWishes;
        }

        if (blessedElement) {
            blessedElement.textContent = blessedWishes;
        }
    }

    loadWishes() {
        const wishes = storage.getWishes();
        const wishesList = document.getElementById('wishesList');
        const emptyState = document.getElementById('wishesEmptyState');

        if (!wishesList) return;

        // 篩選願望
        let filteredWishes = wishes;
        if (this.currentFilter === 'blessed') {
            filteredWishes = wishes.filter(w => w.blessedCount > 0);
        } else if (this.currentFilter === 'pending') {
            filteredWishes = wishes.filter(w => w.blessedCount === 0);
        }

        if (filteredWishes.length === 0) {
            wishesList.style.display = 'none';
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        if (emptyState) emptyState.style.display = 'none';
        wishesList.style.display = 'flex';
        wishesList.innerHTML = '';

        filteredWishes.forEach(wish => {
            const wishItem = this.createWishItem(wish);
            wishesList.appendChild(wishItem);
        });
    }

    createWishItem(wish) {
        const item = document.createElement('div');
        item.className = `wish-item ${wish.blessedCount > 0 ? 'blessed' : ''}`;
        item.dataset.wishId = wish.id;

        const categoryTag = wish.category 
            ? `<span class="wish-category">${this.getCategoryName(wish.category)}</span>`
            : '';

        const blessingCount = wish.blessedCount > 0 
            ? `<span class="wish-blessing-count">已祝福 ${wish.blessedCount} 次</span>`
            : '<span class="wish-blessing-count">待祝福</span>';

        item.innerHTML = `
            <div class="wish-header">
                ${categoryTag}
                <div class="wish-actions">
                    <button class="wish-action-btn" onclick="editWish('${wish.id}')">
                        <svg viewBox="0 0 24 24">
                            <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="wish-text">${wish.text}</div>
            <div class="wish-footer">
                <span class="wish-date">${storage.formatDate(wish.createdAt)}</span>
                ${blessingCount}
            </div>
        `;

        return item;
    }

    getCategoryName(category) {
        const categories = {
            health: '健康',
            family: '家庭',
            career: '事業',
            study: '學業',
            peace: '平安',
            other: '其他'
        };
        return categories[category] || category;
    }

    setFilter(filter) {
        this.currentFilter = filter;
        
        // 更新按鈕狀態
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        // 重新加載願望列表
        this.loadWishes();
    }

    addWish() {
        const textInput = document.getElementById('wishText');
        const categorySelect = document.getElementById('wishCategory');

        const text = textInput.value.trim();
        const category = categorySelect.value;

        if (!text) {
            MantraUtils.showToast('請輸入願望內容');
            return;
        }

        const wish = {
            text,
            category: category || null
        };

        if (storage.addWish(wish)) {
            MantraUtils.showToast('願望添加成功！');
            this.hideAddWish();
            this.loadWishStats();
            this.loadWishes();

            // 清空表單
            textInput.value = '';
            categorySelect.value = '';
        } else {
            MantraUtils.showToast('添加失敗，請重試');
        }
    }

    editWish(wishId) {
        const wish = storage.getWish(wishId);
        if (!wish) return;

        this.editingWishId = wishId;

        const textInput = document.getElementById('editWishText');
        const categorySelect = document.getElementById('editWishCategory');

        if (textInput) textInput.value = wish.text;
        if (categorySelect) categorySelect.value = wish.category || '';

        this.showEditWish();
    }

    updateWish() {
        if (!this.editingWishId) return;

        const textInput = document.getElementById('editWishText');
        const categorySelect = document.getElementById('editWishCategory');

        const text = textInput.value.trim();
        const category = categorySelect.value;

        if (!text) {
            MantraUtils.showToast('請輸入願望內容');
            return;
        }

        const updates = {
            text,
            category: category || null
        };

        if (storage.updateWish(this.editingWishId, updates)) {
            MantraUtils.showToast('願望更新成功！');
            this.hideEditWish();
            this.loadWishes();
        } else {
            MantraUtils.showToast('更新失敗，請重試');
        }
    }

    deleteWish() {
        if (!this.editingWishId) return;

        if (confirm('確定要刪除這個願望嗎？')) {
            if (storage.deleteWish(this.editingWishId)) {
                MantraUtils.showToast('願望已刪除');
                this.hideEditWish();
                this.loadWishStats();
                this.loadWishes();
            } else {
                MantraUtils.showToast('刪除失敗，請重試');
            }
        }
    }

    loadBlessingRecords() {
        const blessings = storage.getBlessings();
        const recordsList = document.getElementById('recordsList');
        const emptyState = document.getElementById('recordsEmptyState');

        if (!recordsList) return;

        if (blessings.length === 0) {
            recordsList.style.display = 'none';
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        if (emptyState) emptyState.style.display = 'none';
        recordsList.style.display = 'flex';
        recordsList.innerHTML = '';

        // 顯示最近的 5 條記錄
        const recentBlessings = blessings
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        recentBlessings.forEach(blessing => {
            const recordItem = this.createRecordItem(blessing);
            recordsList.appendChild(recordItem);
        });
    }

    createRecordItem(blessing) {
        const item = document.createElement('div');
        item.className = 'record-item';
        item.dataset.blessingId = blessing.id;

        const project = storage.getProject(blessing.projectId);
        const projectTitle = project ? project.title : '未知項目';

        const wishCount = blessing.wishIds ? blessing.wishIds.length : 0;
        const wishText = wishCount > 0 ? `${wishCount} 個願望` : '無願望';

        item.innerHTML = `
            <div class="record-header">
                <span class="record-project">${projectTitle}</span>
                <span class="record-count">${blessing.count}</span>
            </div>
            <div class="record-wishes">灌注到 ${wishText}</div>
            <div class="record-date">${storage.formatDate(blessing.createdAt)}</div>
        `;

        item.addEventListener('click', () => {
            this.showRecordDetail(blessing);
        });

        return item;
    }

    showRecordDetail(blessing) {
        const modal = document.getElementById('recordDetailModal');
        const detailElement = document.getElementById('recordDetail');

        if (!modal || !detailElement) return;

        const project = storage.getProject(blessing.projectId);
        const projectTitle = project ? project.title : '未知項目';

        let wishesHtml = '';
        if (blessing.wishIds && blessing.wishIds.length > 0) {
            wishesHtml = '<div class="blessed-wishes-list">';
            blessing.wishIds.forEach(wishId => {
                const wish = storage.getWish(wishId);
                if (wish) {
                    wishesHtml += `<div class="blessed-wish-item">${wish.text}</div>`;
                }
            });
            wishesHtml += '</div>';
        }

        detailElement.innerHTML = `
            <h4>祝福記錄詳情</h4>
            <div class="detail-item">
                <span class="detail-label">修行項目</span>
                <span class="detail-value">${projectTitle}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">持誦次數</span>
                <span class="detail-value">${blessing.count}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">祝福時間</span>
                <span class="detail-value">${new Date(blessing.createdAt).toLocaleString('zh-TW')}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">灌注願望</span>
                <span class="detail-value">${blessing.wishIds ? blessing.wishIds.length : 0} 個</span>
            </div>
            ${wishesHtml}
        `;

        modal.style.display = 'flex';
    }

    showAddWish() {
        const modal = document.getElementById('addWishModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    hideAddWish() {
        const modal = document.getElementById('addWishModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    showEditWish() {
        const modal = document.getElementById('editWishModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    hideEditWish() {
        const modal = document.getElementById('editWishModal');
        if (modal) {
            modal.style.display = 'none';
        }
        this.editingWishId = null;
    }

    hideRecordDetail() {
        const modal = document.getElementById('recordDetailModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    goBack() {
        window.location.href = 'index.html';
    }

    goHome() {
        window.location.href = 'index.html';
    }

    showHistory() {
        MantraUtils.showToast('歷史頁面開發中...');
    }

    showStats() {
        MantraUtils.showToast('統計頁面開發中...');
    }

    showAllRecords() {
        MantraUtils.showToast('查看全部記錄功能開發中...');
    }
}

// 全局函數
function showAddWish() {
    if (window.wishListPage) {
        window.wishListPage.showAddWish();
    }
}

function hideAddWish() {
    if (window.wishListPage) {
        window.wishListPage.hideAddWish();
    }
}

function editWish(wishId) {
    if (window.wishListPage) {
        window.wishListPage.editWish(wishId);
    }
}

function hideEditWish() {
    if (window.wishListPage) {
        window.wishListPage.hideEditWish();
    }
}

function deleteWish() {
    if (window.wishListPage) {
        window.wishListPage.deleteWish();
    }
}

function hideRecordDetail() {
    if (window.wishListPage) {
        window.wishListPage.hideRecordDetail();
    }
}

function goBack() {
    if (window.wishListPage) {
        window.wishListPage.goBack();
    }
}

function goHome() {
    if (window.wishListPage) {
        window.wishListPage.goHome();
    }
}

function showHistory() {
    if (window.wishListPage) {
        window.wishListPage.showHistory();
    }
}

function showStats() {
    if (window.wishListPage) {
        window.wishListPage.showStats();
    }
}

function showAllRecords() {
    if (window.wishListPage) {
        window.wishListPage.showAllRecords();
    }
}

// 初始化願望清單頁面
document.addEventListener('DOMContentLoaded', () => {
    window.wishListPage = new WishListPage();
});
