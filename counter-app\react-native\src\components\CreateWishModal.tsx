import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Colors, ColorUtils } from '../constants/Colors';
import { AudioService } from '../services/AudioService';
import { WishCategory } from '../types';

interface CreateWishModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: {
    text: string;
    category?: WishCategory;
  }) => void;
}

const CreateWishModal: React.FC<CreateWishModalProps> = ({
  visible,
  onClose,
  onSubmit,
}) => {
  const [text, setText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<WishCategory | undefined>();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories: { key: WishCategory; label: string; icon: keyof typeof Ionicons.glyphMap }[] = [
    { key: 'health', label: '健康', icon: 'fitness-outline' },
    { key: 'family', label: '家庭', icon: 'home-outline' },
    { key: 'career', label: '事業', icon: 'briefcase-outline' },
    { key: 'study', label: '學業', icon: 'school-outline' },
    { key: 'peace', label: '平安', icon: 'shield-outline' },
    { key: 'other', label: '其他', icon: 'ellipsis-horizontal-outline' },
  ];

  const handleClose = async () => {
    await AudioService.buttonPress();
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setText('');
    setSelectedCategory(undefined);
    setIsSubmitting(false);
  };

  const handleCategorySelect = async (category: WishCategory) => {
    await AudioService.buttonPress();
    setSelectedCategory(selectedCategory === category ? undefined : category);
  };

  const validateForm = () => {
    if (!text.trim()) {
      Alert.alert('錯誤', '請輸入願望內容');
      return false;
    }

    if (text.trim().length < 5) {
      Alert.alert('錯誤', '願望內容至少需要5個字符');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    await AudioService.buttonPress();

    try {
      onSubmit({
        text: text.trim(),
        category: selectedCategory,
      });
      resetForm();
    } catch (error) {
      console.error('Failed to submit wish:', error);
      Alert.alert('錯誤', '創建願望失敗，請重試');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={styles.keyboardView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* 頂部導航 */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleClose}
            >
              <Ionicons name="close" size={24} color={Colors.textSecondary} />
            </TouchableOpacity>
            
            <Text style={styles.headerTitle}>添加願望</Text>
            
            <TouchableOpacity
              style={[
                styles.submitButton,
                !text.trim() && styles.submitButtonDisabled,
              ]}
              onPress={handleSubmit}
              disabled={!text.trim() || isSubmitting}
            >
              <Text
                style={[
                  styles.submitButtonText,
                  !text.trim() && styles.submitButtonTextDisabled,
                ]}
              >
                {isSubmitting ? '添加中...' : '添加'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* 願望內容 */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>願望內容 *</Text>
              <TextInput
                style={styles.textArea}
                value={text}
                onChangeText={setText}
                placeholder="寫下您的願望..."
                placeholderTextColor={Colors.textTertiary}
                multiline
                numberOfLines={4}
                maxLength={200}
                textAlignVertical="top"
              />
              <Text style={styles.charCount}>{text.length}/200</Text>
            </View>

            {/* 願望分類 */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>願望分類（可選）</Text>
              <View style={styles.categoriesGrid}>
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category.key}
                    style={[
                      styles.categoryButton,
                      selectedCategory === category.key && styles.categoryButtonActive,
                      {
                        borderColor: selectedCategory === category.key 
                          ? ColorUtils.getWishCategoryColor(category.key)
                          : Colors.border,
                        backgroundColor: selectedCategory === category.key
                          ? ColorUtils.getWishCategoryColor(category.key) + '20'
                          : Colors.white,
                      },
                    ]}
                    onPress={() => handleCategorySelect(category.key)}
                  >
                    <Ionicons
                      name={category.icon}
                      size={20}
                      color={
                        selectedCategory === category.key
                          ? ColorUtils.getWishCategoryColor(category.key)
                          : Colors.textSecondary
                      }
                    />
                    <Text
                      style={[
                        styles.categoryText,
                        {
                          color: selectedCategory === category.key
                            ? ColorUtils.getWishCategoryColor(category.key)
                            : Colors.textSecondary,
                        },
                      ]}
                    >
                      {category.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* 提示信息 */}
            <View style={styles.tipContainer}>
              <View style={styles.tipContent}>
                <Ionicons
                  name="bulb-outline"
                  size={20}
                  color={Colors.primary}
                  style={styles.tipIcon}
                />
                <View style={styles.tipTextContainer}>
                  <Text style={styles.tipTitle}>願望建議</Text>
                  <Text style={styles.tipText}>
                    • 願望要具體明確，避免過於籠統{'\n'}
                    • 可以是為自己、家人或眾生的祈願{'\n'}
                    • 完成修行後可以將功德灌注到願望中{'\n'}
                    • 相信願力的力量，持續修行必有感應
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  submitButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: Colors.primary,
  },
  submitButtonDisabled: {
    backgroundColor: Colors.gray200,
  },
  submitButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
  },
  submitButtonTextDisabled: {
    color: Colors.textTertiary,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  formGroup: {
    marginTop: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  textArea: {
    borderWidth: 2,
    borderColor: Colors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.textPrimary,
    backgroundColor: Colors.white,
    height: 100,
    textAlignVertical: 'top',
  },
  charCount: {
    fontSize: 12,
    color: Colors.textTertiary,
    textAlign: 'right',
    marginTop: 4,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    borderWidth: 2,
    gap: 6,
    minWidth: 80,
  },
  categoryButtonActive: {
    // Styles applied dynamically
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
  },
  tipContainer: {
    marginTop: 32,
    marginBottom: 24,
  },
  tipContent: {
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
  },
  tipIcon: {
    marginBottom: 8,
  },
  tipTextContainer: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary,
    marginBottom: 8,
  },
  tipText: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textSecondary,
    lineHeight: 18,
  },
});

export default CreateWishModal;
