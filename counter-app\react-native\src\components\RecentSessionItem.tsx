import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '../constants/Colors';
import { Session, Project } from '../types';
import { StorageService } from '../services/StorageService';

interface RecentSessionItemProps {
  session: Session;
  onPress: () => void;
}

const RecentSessionItem: React.FC<RecentSessionItemProps> = ({
  session,
  onPress,
}) => {
  const [project, setProject] = useState<Project | null>(null);

  useEffect(() => {
    loadProject();
  }, [session.projectId]);

  const loadProject = async () => {
    try {
      const projectData = await StorageService.getProject(session.projectId);
      setProject(projectData);
    } catch (error) {
      console.error('Failed to load project:', error);
    }
  };

  const getCompletionRate = () => {
    return session.target > 0 ? (session.count / session.target) * 100 : 0;
  };

  const getStatusIcon = () => {
    if (session.completed) {
      return 'checkmark-circle';
    } else if (getCompletionRate() >= 50) {
      return 'time';
    } else {
      return 'pause-circle';
    }
  };

  const getStatusColor = () => {
    if (session.completed) {
      return Colors.success;
    } else if (getCompletionRate() >= 50) {
      return Colors.warning;
    } else {
      return Colors.textSecondary;
    }
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* 狀態指示器 */}
      <View style={styles.statusIndicator}>
        <Ionicons
          name={getStatusIcon()}
          size={20}
          color={getStatusColor()}
        />
      </View>

      {/* 會期信息 */}
      <View style={styles.sessionInfo}>
        <Text style={styles.projectTitle} numberOfLines={1}>
          {project?.title || '未知項目'}
        </Text>
        
        <View style={styles.sessionDetails}>
          <Text style={styles.countText}>
            {session.count} / {session.target}
          </Text>
          <Text style={styles.separator}>•</Text>
          <Text style={styles.durationText}>
            {StorageService.formatDuration(session.duration)}
          </Text>
        </View>

        <Text style={styles.dateText}>
          {StorageService.formatDate(session.createdAt)}
        </Text>
      </View>

      {/* 完成率 */}
      <View style={styles.completionSection}>
        <Text style={[styles.completionText, { color: getStatusColor() }]}>
          {getCompletionRate().toFixed(0)}%
        </Text>
        
        {/* 小型進度條 */}
        <View style={styles.miniProgressBar}>
          <View
            style={[
              styles.miniProgressFill,
              {
                width: `${Math.min(getCompletionRate(), 100)}%`,
                backgroundColor: getStatusColor(),
              },
            ]}
          />
        </View>
      </View>

      {/* 箭頭圖標 */}
      <View style={styles.arrowContainer}>
        <Ionicons
          name="chevron-forward"
          size={16}
          color={Colors.textTertiary}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statusIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  sessionInfo: {
    flex: 1,
    marginRight: 12,
  },
  projectTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  sessionDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  countText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  separator: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textTertiary,
    marginHorizontal: 6,
  },
  durationText: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors.textSecondary,
  },
  dateText: {
    fontSize: 10,
    fontWeight: '400',
    color: Colors.textTertiary,
  },
  completionSection: {
    alignItems: 'flex-end',
    marginRight: 8,
  },
  completionText: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  miniProgressBar: {
    width: 40,
    height: 3,
    backgroundColor: Colors.backgroundTertiary,
    borderRadius: 1.5,
    overflow: 'hidden',
  },
  miniProgressFill: {
    height: '100%',
    borderRadius: 1.5,
  },
  arrowContainer: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default RecentSessionItem;
